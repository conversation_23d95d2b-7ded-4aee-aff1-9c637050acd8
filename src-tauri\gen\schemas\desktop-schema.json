{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CapabilityFile", "description": "Capability formats accepted in a capability file.", "anyOf": [{"description": "A single capability.", "allOf": [{"$ref": "#/definitions/Capability"}]}, {"description": "A list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}, {"description": "A list of capabilities.", "type": "object", "required": ["capabilities"], "properties": {"capabilities": {"description": "The list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}}}], "definitions": {"Capability": {"description": "A grouping and boundary mechanism developers can use to isolate access to the IPC layer.\n\nIt controls application windows' and webviews' fine grained access to the Tauri core, application, or plugin commands. If a webview or its window is not matching any capability then it has no access to the IPC layer at all.\n\nThis can be done to create groups of windows, based on their required system access, which can reduce impact of frontend vulnerabilities in less privileged windows. Windows can be added to a capability by exact name (e.g. `main-window`) or glob patterns like `*` or `admin-*`. A Window can have none, one, or multiple associated capabilities.\n\n## Example\n\n```json { \"identifier\": \"main-user-files-write\", \"description\": \"This capability allows the `main` window on macOS and Windows access to `filesystem` write related commands and `dialog` commands to enable programmatic access to files selected by the user.\", \"windows\": [ \"main\" ], \"permissions\": [ \"core:default\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] }, ], \"platforms\": [\"macOS\",\"windows\"] } ```", "type": "object", "required": ["identifier", "permissions"], "properties": {"identifier": {"description": "Identifier of the capability.\n\n## Example\n\n`main-user-files-write`", "type": "string"}, "description": {"description": "Description of what the capability is intended to allow on associated windows.\n\nIt should contain a description of what the grouped permissions should allow.\n\n## Example\n\nThis capability allows the `main` window access to `filesystem` write related commands and `dialog` commands to enable programmatic access to files selected by the user.", "default": "", "type": "string"}, "remote": {"description": "Configure remote URLs that can use the capability permissions.\n\nThis setting is optional and defaults to not being set, as our default use case is that the content is served from our local application.\n\n:::caution Make sure you understand the security implications of providing remote sources with local system access. :::\n\n## Example\n\n```json { \"urls\": [\"https://*.mydomain.dev\"] } ```", "anyOf": [{"$ref": "#/definitions/CapabilityRemote"}, {"type": "null"}]}, "local": {"description": "Whether this capability is enabled for local app URLs or not. Defaults to `true`.", "default": true, "type": "boolean"}, "windows": {"description": "List of windows that are affected by this capability. Can be a glob pattern.\n\nIf a window label matches any of the patterns in this list, the capability will be enabled on all the webviews of that window, regardless of the value of [`Self::webviews`].\n\nOn multiwebview windows, prefer specifying [`Self::webviews`] and omitting [`Self::windows`] for a fine grained access control.\n\n## Example\n\n`[\"main\"]`", "type": "array", "items": {"type": "string"}}, "webviews": {"description": "List of webviews that are affected by this capability. Can be a glob pattern.\n\nThe capability will be enabled on all the webviews whose label matches any of the patterns in this list, regardless of whether the webview's window label matches a pattern in [`Self::windows`].\n\n## Example\n\n`[\"sub-webview-one\", \"sub-webview-two\"]`", "type": "array", "items": {"type": "string"}}, "permissions": {"description": "List of permissions attached to this capability.\n\nMust include the plugin name as prefix in the form of `${plugin-name}:${permission-name}`. For commands directly implemented in the application itself only `${permission-name}` is required.\n\n## Example\n\n```json [ \"core:default\", \"shell:allow-open\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] } ] ```", "type": "array", "items": {"$ref": "#/definitions/PermissionEntry"}, "uniqueItems": true}, "platforms": {"description": "Limit which target platforms this capability applies to.\n\nBy default all platforms are targeted.\n\n## Example\n\n`[\"macOS\",\"windows\"]`", "type": ["array", "null"], "items": {"$ref": "#/definitions/Target"}}}}, "CapabilityRemote": {"description": "Configuration for remote URLs that are associated with the capability.", "type": "object", "required": ["urls"], "properties": {"urls": {"description": "Remote domains this capability refers to using the [URLPattern standard](https://urlpattern.spec.whatwg.org/).\n\n## Examples\n\n- \"https://*.mydomain.dev\": allows subdomains of mydomain.dev - \"https://mydomain.dev/api/*\": allows any subpath of mydomain.dev/api", "type": "array", "items": {"type": "string"}}}}, "PermissionEntry": {"description": "An entry for a permission value in a [`Capability`] can be either a raw permission [`Identifier`] or an object that references a permission and extends its scope.", "anyOf": [{"description": "Reference a permission or permission set by identifier.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, {"description": "Reference a permission or permission set by identifier and extends its scope.", "type": "object", "allOf": [{"if": {"properties": {"identifier": {"anyOf": [{"description": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n#### This default permission set includes:\n\n- `create-app-specific-dirs`\n- `read-app-specific-dirs-recursive`\n- `deny-default`", "type": "string", "const": "fs:default", "markdownDescription": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n#### This default permission set includes:\n\n- `create-app-specific-dirs`\n- `read-app-specific-dirs-recursive`\n- `deny-default`"}, {"description": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-index`", "type": "string", "const": "fs:allow-app-meta", "markdownDescription": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-index`"}, {"description": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive read access to the application folders.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app`", "type": "string", "const": "fs:allow-app-read", "markdownDescription": "This allows non-recursive read access to the application folders.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app`"}, {"description": "This allows full recursive read access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-read-recursive", "markdownDescription": "This allows full recursive read access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive write access to the application folders.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app`", "type": "string", "const": "fs:allow-app-write", "markdownDescription": "This allows non-recursive write access to the application folders.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app`"}, {"description": "This allows full recursive write access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-write-recursive", "markdownDescription": "This allows full recursive write access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-index`", "type": "string", "const": "fs:allow-appcache-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive read access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache`", "type": "string", "const": "fs:allow-appcache-read", "markdownDescription": "This allows non-recursive read access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache`"}, {"description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive write access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache`", "type": "string", "const": "fs:allow-appcache-write", "markdownDescription": "This allows non-recursive write access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache`"}, {"description": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-index`", "type": "string", "const": "fs:allow-appconfig-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive read access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig`", "type": "string", "const": "fs:allow-appconfig-read", "markdownDescription": "This allows non-recursive read access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig`"}, {"description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive write access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig`", "type": "string", "const": "fs:allow-appconfig-write", "markdownDescription": "This allows non-recursive write access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig`"}, {"description": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-index`", "type": "string", "const": "fs:allow-appdata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive read access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata`", "type": "string", "const": "fs:allow-appdata-read", "markdownDescription": "This allows non-recursive read access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata`"}, {"description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive write access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata`", "type": "string", "const": "fs:allow-appdata-write", "markdownDescription": "This allows non-recursive write access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata`"}, {"description": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-index`", "type": "string", "const": "fs:allow-applocaldata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata`", "type": "string", "const": "fs:allow-applocaldata-read", "markdownDescription": "This allows non-recursive read access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata`"}, {"description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata`", "type": "string", "const": "fs:allow-applocaldata-write", "markdownDescription": "This allows non-recursive write access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata`"}, {"description": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-index`", "type": "string", "const": "fs:allow-applog-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive read access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog`", "type": "string", "const": "fs:allow-applog-read", "markdownDescription": "This allows non-recursive read access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog`"}, {"description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive write access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog`", "type": "string", "const": "fs:allow-applog-write", "markdownDescription": "This allows non-recursive write access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog`"}, {"description": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-index`", "type": "string", "const": "fs:allow-audio-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-index`"}, {"description": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive read access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio`", "type": "string", "const": "fs:allow-audio-read", "markdownDescription": "This allows non-recursive read access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio`"}, {"description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive write access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio`", "type": "string", "const": "fs:allow-audio-write", "markdownDescription": "This allows non-recursive write access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio`"}, {"description": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-index`", "type": "string", "const": "fs:allow-cache-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-index`"}, {"description": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive read access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache`", "type": "string", "const": "fs:allow-cache-read", "markdownDescription": "This allows non-recursive read access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache`"}, {"description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive write access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache`", "type": "string", "const": "fs:allow-cache-write", "markdownDescription": "This allows non-recursive write access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache`"}, {"description": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-index`", "type": "string", "const": "fs:allow-config-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-index`"}, {"description": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive read access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config`", "type": "string", "const": "fs:allow-config-read", "markdownDescription": "This allows non-recursive read access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config`"}, {"description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive write access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config`", "type": "string", "const": "fs:allow-config-write", "markdownDescription": "This allows non-recursive write access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config`"}, {"description": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-index`", "type": "string", "const": "fs:allow-data-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-index`"}, {"description": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive read access to the `$DATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data`", "type": "string", "const": "fs:allow-data-read", "markdownDescription": "This allows non-recursive read access to the `$DATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data`"}, {"description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive write access to the `$DATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data`", "type": "string", "const": "fs:allow-data-write", "markdownDescription": "This allows non-recursive write access to the `$DATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data`"}, {"description": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-index`", "type": "string", "const": "fs:allow-desktop-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-index`"}, {"description": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive read access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop`", "type": "string", "const": "fs:allow-desktop-read", "markdownDescription": "This allows non-recursive read access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop`"}, {"description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive write access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop`", "type": "string", "const": "fs:allow-desktop-write", "markdownDescription": "This allows non-recursive write access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop`"}, {"description": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-index`", "type": "string", "const": "fs:allow-document-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-index`"}, {"description": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive read access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document`", "type": "string", "const": "fs:allow-document-read", "markdownDescription": "This allows non-recursive read access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document`"}, {"description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive write access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document`", "type": "string", "const": "fs:allow-document-write", "markdownDescription": "This allows non-recursive write access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document`"}, {"description": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-index`", "type": "string", "const": "fs:allow-download-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-index`"}, {"description": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive read access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download`", "type": "string", "const": "fs:allow-download-read", "markdownDescription": "This allows non-recursive read access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download`"}, {"description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive write access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download`", "type": "string", "const": "fs:allow-download-write", "markdownDescription": "This allows non-recursive write access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download`"}, {"description": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-index`", "type": "string", "const": "fs:allow-exe-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-index`"}, {"description": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive read access to the `$EXE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe`", "type": "string", "const": "fs:allow-exe-read", "markdownDescription": "This allows non-recursive read access to the `$EXE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe`"}, {"description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive write access to the `$EXE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe`", "type": "string", "const": "fs:allow-exe-write", "markdownDescription": "This allows non-recursive write access to the `$EXE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe`"}, {"description": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-index`", "type": "string", "const": "fs:allow-font-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-index`"}, {"description": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive read access to the `$FONT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font`", "type": "string", "const": "fs:allow-font-read", "markdownDescription": "This allows non-recursive read access to the `$FONT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font`"}, {"description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive write access to the `$FONT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font`", "type": "string", "const": "fs:allow-font-write", "markdownDescription": "This allows non-recursive write access to the `$FONT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font`"}, {"description": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-index`", "type": "string", "const": "fs:allow-home-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-index`"}, {"description": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive read access to the `$HOME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home`", "type": "string", "const": "fs:allow-home-read", "markdownDescription": "This allows non-recursive read access to the `$HOME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home`"}, {"description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive write access to the `$HOME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home`", "type": "string", "const": "fs:allow-home-write", "markdownDescription": "This allows non-recursive write access to the `$HOME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home`"}, {"description": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-index`", "type": "string", "const": "fs:allow-localdata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-index`"}, {"description": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive read access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata`", "type": "string", "const": "fs:allow-localdata-read", "markdownDescription": "This allows non-recursive read access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata`"}, {"description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive write access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata`", "type": "string", "const": "fs:allow-localdata-write", "markdownDescription": "This allows non-recursive write access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata`"}, {"description": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-index`", "type": "string", "const": "fs:allow-log-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-index`"}, {"description": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive read access to the `$LOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log`", "type": "string", "const": "fs:allow-log-read", "markdownDescription": "This allows non-recursive read access to the `$LOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log`"}, {"description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive write access to the `$LOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log`", "type": "string", "const": "fs:allow-log-write", "markdownDescription": "This allows non-recursive write access to the `$LOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log`"}, {"description": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-index`", "type": "string", "const": "fs:allow-picture-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-index`"}, {"description": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive read access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture`", "type": "string", "const": "fs:allow-picture-read", "markdownDescription": "This allows non-recursive read access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture`"}, {"description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive write access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture`", "type": "string", "const": "fs:allow-picture-write", "markdownDescription": "This allows non-recursive write access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture`"}, {"description": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-index`", "type": "string", "const": "fs:allow-public-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-index`"}, {"description": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive read access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public`", "type": "string", "const": "fs:allow-public-read", "markdownDescription": "This allows non-recursive read access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public`"}, {"description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive write access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public`", "type": "string", "const": "fs:allow-public-write", "markdownDescription": "This allows non-recursive write access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public`"}, {"description": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-index`", "type": "string", "const": "fs:allow-resource-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-index`"}, {"description": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive read access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource`", "type": "string", "const": "fs:allow-resource-read", "markdownDescription": "This allows non-recursive read access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource`"}, {"description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive write access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource`", "type": "string", "const": "fs:allow-resource-write", "markdownDescription": "This allows non-recursive write access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource`"}, {"description": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-index`", "type": "string", "const": "fs:allow-runtime-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-index`"}, {"description": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive read access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime`", "type": "string", "const": "fs:allow-runtime-read", "markdownDescription": "This allows non-recursive read access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime`"}, {"description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive write access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime`", "type": "string", "const": "fs:allow-runtime-write", "markdownDescription": "This allows non-recursive write access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime`"}, {"description": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-index`", "type": "string", "const": "fs:allow-temp-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-index`"}, {"description": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive read access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp`", "type": "string", "const": "fs:allow-temp-read", "markdownDescription": "This allows non-recursive read access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp`"}, {"description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive write access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp`", "type": "string", "const": "fs:allow-temp-write", "markdownDescription": "This allows non-recursive write access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp`"}, {"description": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-index`", "type": "string", "const": "fs:allow-template-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-index`"}, {"description": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive read access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template`", "type": "string", "const": "fs:allow-template-read", "markdownDescription": "This allows non-recursive read access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template`"}, {"description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive write access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template`", "type": "string", "const": "fs:allow-template-write", "markdownDescription": "This allows non-recursive write access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template`"}, {"description": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-index`", "type": "string", "const": "fs:allow-video-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-index`"}, {"description": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-recursive`"}, {"description": "This allows non-recursive read access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video`", "type": "string", "const": "fs:allow-video-read", "markdownDescription": "This allows non-recursive read access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video`"}, {"description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video-recursive`"}, {"description": "This allows non-recursive write access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video`", "type": "string", "const": "fs:allow-video-write", "markdownDescription": "This allows non-recursive write access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video`"}, {"description": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video-recursive`"}, {"description": "This denies access to dangerous Tauri relevant files and folders by default.\n#### This permission set includes:\n\n- `deny-webview-data-linux`\n- `deny-webview-data-windows`", "type": "string", "const": "fs:deny-default", "markdownDescription": "This denies access to dangerous Tauri relevant files and folders by default.\n#### This permission set includes:\n\n- `deny-webview-data-linux`\n- `deny-webview-data-windows`"}, {"description": "Enables the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-copy-file", "markdownDescription": "Enables the copy_file command without any pre-configured scope."}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "fs:allow-create", "markdownDescription": "Enables the create command without any pre-configured scope."}, {"description": "Enables the exists command without any pre-configured scope.", "type": "string", "const": "fs:allow-exists", "markdownDescription": "Enables the exists command without any pre-configured scope."}, {"description": "Enables the fstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-fstat", "markdownDescription": "Enables the fstat command without any pre-configured scope."}, {"description": "Enables the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-ftruncate", "markdownDescription": "Enables the ftruncate command without any pre-configured scope."}, {"description": "Enables the lstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-lstat", "markdownDescription": "Enables the lstat command without any pre-configured scope."}, {"description": "Enables the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:allow-mkdir", "markdownDescription": "Enables the mkdir command without any pre-configured scope."}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "fs:allow-open", "markdownDescription": "Enables the open command without any pre-configured scope."}, {"description": "Enables the read command without any pre-configured scope.", "type": "string", "const": "fs:allow-read", "markdownDescription": "Enables the read command without any pre-configured scope."}, {"description": "Enables the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-dir", "markdownDescription": "Enables the read_dir command without any pre-configured scope."}, {"description": "Enables the read_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-file", "markdownDescription": "Enables the read_file command without any pre-configured scope."}, {"description": "Enables the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file", "markdownDescription": "Enables the read_text_file command without any pre-configured scope."}, {"description": "Enables the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines", "markdownDescription": "Enables the read_text_file_lines command without any pre-configured scope."}, {"description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines-next", "markdownDescription": "Enables the read_text_file_lines_next command without any pre-configured scope."}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "fs:allow-remove", "markdownDescription": "Enables the remove command without any pre-configured scope."}, {"description": "Enables the rename command without any pre-configured scope.", "type": "string", "const": "fs:allow-rename", "markdownDescription": "Enables the rename command without any pre-configured scope."}, {"description": "Enables the seek command without any pre-configured scope.", "type": "string", "const": "fs:allow-seek", "markdownDescription": "Enables the seek command without any pre-configured scope."}, {"description": "Enables the size command without any pre-configured scope.", "type": "string", "const": "fs:allow-size", "markdownDescription": "Enables the size command without any pre-configured scope."}, {"description": "Enables the stat command without any pre-configured scope.", "type": "string", "const": "fs:allow-stat", "markdownDescription": "Enables the stat command without any pre-configured scope."}, {"description": "Enables the truncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-truncate", "markdownDescription": "Enables the truncate command without any pre-configured scope."}, {"description": "Enables the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:allow-unwatch", "markdownDescription": "Enables the unwatch command without any pre-configured scope."}, {"description": "Enables the watch command without any pre-configured scope.", "type": "string", "const": "fs:allow-watch", "markdownDescription": "Enables the watch command without any pre-configured scope."}, {"description": "Enables the write command without any pre-configured scope.", "type": "string", "const": "fs:allow-write", "markdownDescription": "Enables the write command without any pre-configured scope."}, {"description": "Enables the write_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-file", "markdownDescription": "Enables the write_file command without any pre-configured scope."}, {"description": "Enables the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-text-file", "markdownDescription": "Enables the write_text_file command without any pre-configured scope."}, {"description": "This permissions allows to create the application specific directories.\n", "type": "string", "const": "fs:create-app-specific-dirs", "markdownDescription": "This permissions allows to create the application specific directories.\n"}, {"description": "Denies the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-copy-file", "markdownDescription": "Denies the copy_file command without any pre-configured scope."}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "fs:deny-create", "markdownDescription": "Denies the create command without any pre-configured scope."}, {"description": "Denies the exists command without any pre-configured scope.", "type": "string", "const": "fs:deny-exists", "markdownDescription": "Denies the exists command without any pre-configured scope."}, {"description": "Denies the fstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-fstat", "markdownDescription": "Denies the fstat command without any pre-configured scope."}, {"description": "Denies the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-ftruncate", "markdownDescription": "Denies the ftruncate command without any pre-configured scope."}, {"description": "Denies the lstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-lstat", "markdownDescription": "Denies the lstat command without any pre-configured scope."}, {"description": "Denies the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:deny-mkdir", "markdownDescription": "Denies the mkdir command without any pre-configured scope."}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "fs:deny-open", "markdownDescription": "Denies the open command without any pre-configured scope."}, {"description": "Denies the read command without any pre-configured scope.", "type": "string", "const": "fs:deny-read", "markdownDescription": "Denies the read command without any pre-configured scope."}, {"description": "Denies the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-dir", "markdownDescription": "Denies the read_dir command without any pre-configured scope."}, {"description": "Denies the read_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-file", "markdownDescription": "Denies the read_file command without any pre-configured scope."}, {"description": "Denies the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file", "markdownDescription": "Denies the read_text_file command without any pre-configured scope."}, {"description": "Denies the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines", "markdownDescription": "Denies the read_text_file_lines command without any pre-configured scope."}, {"description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines-next", "markdownDescription": "Denies the read_text_file_lines_next command without any pre-configured scope."}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "fs:deny-remove", "markdownDescription": "Denies the remove command without any pre-configured scope."}, {"description": "Denies the rename command without any pre-configured scope.", "type": "string", "const": "fs:deny-rename", "markdownDescription": "Denies the rename command without any pre-configured scope."}, {"description": "Denies the seek command without any pre-configured scope.", "type": "string", "const": "fs:deny-seek", "markdownDescription": "Denies the seek command without any pre-configured scope."}, {"description": "Denies the size command without any pre-configured scope.", "type": "string", "const": "fs:deny-size", "markdownDescription": "Denies the size command without any pre-configured scope."}, {"description": "Denies the stat command without any pre-configured scope.", "type": "string", "const": "fs:deny-stat", "markdownDescription": "Denies the stat command without any pre-configured scope."}, {"description": "Denies the truncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-truncate", "markdownDescription": "Denies the truncate command without any pre-configured scope."}, {"description": "Denies the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:deny-unwatch", "markdownDescription": "Denies the unwatch command without any pre-configured scope."}, {"description": "Denies the watch command without any pre-configured scope.", "type": "string", "const": "fs:deny-watch", "markdownDescription": "Denies the watch command without any pre-configured scope."}, {"description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-linux", "markdownDescription": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered."}, {"description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-windows", "markdownDescription": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered."}, {"description": "Denies the write command without any pre-configured scope.", "type": "string", "const": "fs:deny-write", "markdownDescription": "Denies the write command without any pre-configured scope."}, {"description": "Denies the write_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-file", "markdownDescription": "Denies the write_file command without any pre-configured scope."}, {"description": "Denies the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-text-file", "markdownDescription": "Denies the write_text_file command without any pre-configured scope."}, {"description": "This enables all read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-all", "markdownDescription": "This enables all read related commands without any pre-configured accessible paths."}, {"description": "This permission allows recursive read functionality on the application\nspecific base directories. \n", "type": "string", "const": "fs:read-app-specific-dirs-recursive", "markdownDescription": "This permission allows recursive read functionality on the application\nspecific base directories. \n"}, {"description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-dirs", "markdownDescription": "This enables directory read and file metadata related commands without any pre-configured accessible paths."}, {"description": "This enables file read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-files", "markdownDescription": "This enables file read related commands without any pre-configured accessible paths."}, {"description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-meta", "markdownDescription": "This enables all index or metadata related commands without any pre-configured accessible paths."}, {"description": "An empty permission you can use to modify the global scope.", "type": "string", "const": "fs:scope", "markdownDescription": "An empty permission you can use to modify the global scope."}, {"description": "This scope permits access to all files and list content of top level directories in the application folders.", "type": "string", "const": "fs:scope-app", "markdownDescription": "This scope permits access to all files and list content of top level directories in the application folders."}, {"description": "This scope permits to list all files and folders in the application directories.", "type": "string", "const": "fs:scope-app-index", "markdownDescription": "This scope permits to list all files and folders in the application directories."}, {"description": "This scope permits recursive access to the complete application folders, including sub directories and files.", "type": "string", "const": "fs:scope-app-recursive", "markdownDescription": "This scope permits recursive access to the complete application folders, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder.", "type": "string", "const": "fs:scope-appcache", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder."}, {"description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "type": "string", "const": "fs:scope-appcache-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPCACHE`folder."}, {"description": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appcache-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder.", "type": "string", "const": "fs:scope-appconfig", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder."}, {"description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "type": "string", "const": "fs:scope-appconfig-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPCONFIG`folder."}, {"description": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appconfig-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder.", "type": "string", "const": "fs:scope-appdata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "type": "string", "const": "fs:scope-appdata-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPDATA`folder."}, {"description": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appdata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:scope-applocaldata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "type": "string", "const": "fs:scope-applocaldata-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder."}, {"description": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applocaldata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder.", "type": "string", "const": "fs:scope-applog", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder."}, {"description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "type": "string", "const": "fs:scope-applog-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPLOG`folder."}, {"description": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applog-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder.", "type": "string", "const": "fs:scope-audio", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder."}, {"description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "type": "string", "const": "fs:scope-audio-index", "markdownDescription": "This scope permits to list all files and folders in the `$AUDIO`folder."}, {"description": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-audio-recursive", "markdownDescription": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder.", "type": "string", "const": "fs:scope-cache", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder."}, {"description": "This scope permits to list all files and folders in the `$CACHE`folder.", "type": "string", "const": "fs:scope-cache-index", "markdownDescription": "This scope permits to list all files and folders in the `$CACHE`folder."}, {"description": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-cache-recursive", "markdownDescription": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder.", "type": "string", "const": "fs:scope-config", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder."}, {"description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "type": "string", "const": "fs:scope-config-index", "markdownDescription": "This scope permits to list all files and folders in the `$CONFIG`folder."}, {"description": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-config-recursive", "markdownDescription": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DATA` folder.", "type": "string", "const": "fs:scope-data", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DATA` folder."}, {"description": "This scope permits to list all files and folders in the `$DATA`folder.", "type": "string", "const": "fs:scope-data-index", "markdownDescription": "This scope permits to list all files and folders in the `$DATA`folder."}, {"description": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-data-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder.", "type": "string", "const": "fs:scope-desktop", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder."}, {"description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "type": "string", "const": "fs:scope-desktop-index", "markdownDescription": "This scope permits to list all files and folders in the `$DESKTOP`folder."}, {"description": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-desktop-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder.", "type": "string", "const": "fs:scope-document", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder."}, {"description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "type": "string", "const": "fs:scope-document-index", "markdownDescription": "This scope permits to list all files and folders in the `$DOCUMENT`folder."}, {"description": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-document-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder.", "type": "string", "const": "fs:scope-download", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder."}, {"description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "type": "string", "const": "fs:scope-download-index", "markdownDescription": "This scope permits to list all files and folders in the `$DOWNLOAD`folder."}, {"description": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "type": "string", "const": "fs:scope-download-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$EXE` folder.", "type": "string", "const": "fs:scope-exe", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$EXE` folder."}, {"description": "This scope permits to list all files and folders in the `$EXE`folder.", "type": "string", "const": "fs:scope-exe-index", "markdownDescription": "This scope permits to list all files and folders in the `$EXE`folder."}, {"description": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-exe-recursive", "markdownDescription": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$FONT` folder.", "type": "string", "const": "fs:scope-font", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$FONT` folder."}, {"description": "This scope permits to list all files and folders in the `$FONT`folder.", "type": "string", "const": "fs:scope-font-index", "markdownDescription": "This scope permits to list all files and folders in the `$FONT`folder."}, {"description": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-font-recursive", "markdownDescription": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$HOME` folder.", "type": "string", "const": "fs:scope-home", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$HOME` folder."}, {"description": "This scope permits to list all files and folders in the `$HOME`folder.", "type": "string", "const": "fs:scope-home-index", "markdownDescription": "This scope permits to list all files and folders in the `$HOME`folder."}, {"description": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-home-recursive", "markdownDescription": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder.", "type": "string", "const": "fs:scope-localdata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "type": "string", "const": "fs:scope-localdata-index", "markdownDescription": "This scope permits to list all files and folders in the `$LOCALDATA`folder."}, {"description": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-localdata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOG` folder.", "type": "string", "const": "fs:scope-log", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$LOG` folder."}, {"description": "This scope permits to list all files and folders in the `$LOG`folder.", "type": "string", "const": "fs:scope-log-index", "markdownDescription": "This scope permits to list all files and folders in the `$LOG`folder."}, {"description": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-log-recursive", "markdownDescription": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder.", "type": "string", "const": "fs:scope-picture", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder."}, {"description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "type": "string", "const": "fs:scope-picture-index", "markdownDescription": "This scope permits to list all files and folders in the `$PICTURE`folder."}, {"description": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-picture-recursive", "markdownDescription": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder.", "type": "string", "const": "fs:scope-public", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder."}, {"description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "type": "string", "const": "fs:scope-public-index", "markdownDescription": "This scope permits to list all files and folders in the `$PUBLIC`folder."}, {"description": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "type": "string", "const": "fs:scope-public-recursive", "markdownDescription": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder.", "type": "string", "const": "fs:scope-resource", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder."}, {"description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "type": "string", "const": "fs:scope-resource-index", "markdownDescription": "This scope permits to list all files and folders in the `$RESOURCE`folder."}, {"description": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-resource-recursive", "markdownDescription": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder.", "type": "string", "const": "fs:scope-runtime", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder."}, {"description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "type": "string", "const": "fs:scope-runtime-index", "markdownDescription": "This scope permits to list all files and folders in the `$RUNTIME`folder."}, {"description": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-runtime-recursive", "markdownDescription": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder.", "type": "string", "const": "fs:scope-temp", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder."}, {"description": "This scope permits to list all files and folders in the `$TEMP`folder.", "type": "string", "const": "fs:scope-temp-index", "markdownDescription": "This scope permits to list all files and folders in the `$TEMP`folder."}, {"description": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-temp-recursive", "markdownDescription": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder.", "type": "string", "const": "fs:scope-template", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder."}, {"description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "type": "string", "const": "fs:scope-template-index", "markdownDescription": "This scope permits to list all files and folders in the `$TEMPLATE`folder."}, {"description": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-template-recursive", "markdownDescription": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder.", "type": "string", "const": "fs:scope-video", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder."}, {"description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "type": "string", "const": "fs:scope-video-index", "markdownDescription": "This scope permits to list all files and folders in the `$VIDEO`folder."}, {"description": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-video-recursive", "markdownDescription": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files."}, {"description": "This enables all write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-all", "markdownDescription": "This enables all write related commands without any pre-configured accessible paths."}, {"description": "This enables all file write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-files", "markdownDescription": "This enables all file write related commands without any pre-configured accessible paths."}]}}}, "then": {"properties": {"allow": {"items": {"title": "FsScopeEntry", "description": "FS scope entry.", "anyOf": [{"description": "A path that can be accessed by the webview when using the fs APIs. FS scope path pattern.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, {"type": "object", "required": ["path"], "properties": {"path": {"description": "A path that can be accessed by the webview when using the fs APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}}]}}, "deny": {"items": {"title": "FsScopeEntry", "description": "FS scope entry.", "anyOf": [{"description": "A path that can be accessed by the webview when using the fs APIs. FS scope path pattern.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, {"type": "object", "required": ["path"], "properties": {"path": {"description": "A path that can be accessed by the webview when using the fs APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"if": {"properties": {"identifier": {"anyOf": [{"description": "This permission set allows opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application\nas well as reveal file in directories using default file explorer\n#### This default permission set includes:\n\n- `allow-open-url`\n- `allow-reveal-item-in-dir`\n- `allow-default-urls`", "type": "string", "const": "opener:default", "markdownDescription": "This permission set allows opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application\nas well as reveal file in directories using default file explorer\n#### This default permission set includes:\n\n- `allow-open-url`\n- `allow-reveal-item-in-dir`\n- `allow-default-urls`"}, {"description": "This enables opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application.", "type": "string", "const": "opener:allow-default-urls", "markdownDescription": "This enables opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application."}, {"description": "Enables the open_path command without any pre-configured scope.", "type": "string", "const": "opener:allow-open-path", "markdownDescription": "Enables the open_path command without any pre-configured scope."}, {"description": "Enables the open_url command without any pre-configured scope.", "type": "string", "const": "opener:allow-open-url", "markdownDescription": "Enables the open_url command without any pre-configured scope."}, {"description": "Enables the reveal_item_in_dir command without any pre-configured scope.", "type": "string", "const": "opener:allow-reveal-item-in-dir", "markdownDescription": "Enables the reveal_item_in_dir command without any pre-configured scope."}, {"description": "Denies the open_path command without any pre-configured scope.", "type": "string", "const": "opener:deny-open-path", "markdownDescription": "Denies the open_path command without any pre-configured scope."}, {"description": "Denies the open_url command without any pre-configured scope.", "type": "string", "const": "opener:deny-open-url", "markdownDescription": "Denies the open_url command without any pre-configured scope."}, {"description": "Denies the reveal_item_in_dir command without any pre-configured scope.", "type": "string", "const": "opener:deny-reveal-item-in-dir", "markdownDescription": "Denies the reveal_item_in_dir command without any pre-configured scope."}]}}}, "then": {"properties": {"allow": {"items": {"title": "OpenerScopeEntry", "description": "Opener scope entry.", "anyOf": [{"type": "object", "required": ["url"], "properties": {"app": {"description": "An application to open this url with, for example: firefox.", "allOf": [{"$ref": "#/definitions/Application"}]}, "url": {"description": "A URL that can be opened by the webview when using the Opener APIs.\n\nWildcards can be used following the UNIX glob pattern.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}}, {"type": "object", "required": ["path"], "properties": {"app": {"description": "An application to open this path with, for example: xdg-open.", "allOf": [{"$ref": "#/definitions/Application"}]}, "path": {"description": "A path that can be opened by the webview when using the Opener APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}}]}}, "deny": {"items": {"title": "OpenerScopeEntry", "description": "Opener scope entry.", "anyOf": [{"type": "object", "required": ["url"], "properties": {"app": {"description": "An application to open this url with, for example: firefox.", "allOf": [{"$ref": "#/definitions/Application"}]}, "url": {"description": "A URL that can be opened by the webview when using the Opener APIs.\n\nWildcards can be used following the UNIX glob pattern.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}}, {"type": "object", "required": ["path"], "properties": {"app": {"description": "An application to open this path with, for example: xdg-open.", "allOf": [{"$ref": "#/definitions/Application"}]}, "path": {"description": "A path that can be opened by the webview when using the Opener APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"if": {"properties": {"identifier": {"anyOf": [{"description": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality with a reasonable\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n\n#### This default permission set includes:\n\n- `allow-open`", "type": "string", "const": "shell:default", "markdownDescription": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality with a reasonable\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n\n#### This default permission set includes:\n\n- `allow-open`"}, {"description": "Enables the execute command without any pre-configured scope.", "type": "string", "const": "shell:allow-execute", "markdownDescription": "Enables the execute command without any pre-configured scope."}, {"description": "Enables the kill command without any pre-configured scope.", "type": "string", "const": "shell:allow-kill", "markdownDescription": "Enables the kill command without any pre-configured scope."}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "shell:allow-open", "markdownDescription": "Enables the open command without any pre-configured scope."}, {"description": "Enables the spawn command without any pre-configured scope.", "type": "string", "const": "shell:allow-spawn", "markdownDescription": "Enables the spawn command without any pre-configured scope."}, {"description": "Enables the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:allow-stdin-write", "markdownDescription": "Enables the stdin_write command without any pre-configured scope."}, {"description": "Denies the execute command without any pre-configured scope.", "type": "string", "const": "shell:deny-execute", "markdownDescription": "Denies the execute command without any pre-configured scope."}, {"description": "Denies the kill command without any pre-configured scope.", "type": "string", "const": "shell:deny-kill", "markdownDescription": "Denies the kill command without any pre-configured scope."}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "shell:deny-open", "markdownDescription": "Denies the open command without any pre-configured scope."}, {"description": "Denies the spawn command without any pre-configured scope.", "type": "string", "const": "shell:deny-spawn", "markdownDescription": "Denies the spawn command without any pre-configured scope."}, {"description": "Denies the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:deny-stdin-write", "markdownDescription": "Denies the stdin_write command without any pre-configured scope."}]}}}, "then": {"properties": {"allow": {"items": {"title": "ShellScopeEntry", "description": "Shell scope entry.", "anyOf": [{"type": "object", "required": ["cmd", "name"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}}, "additionalProperties": false}, {"type": "object", "required": ["name", "sidecar"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "sidecar": {"description": "If this command is a sidecar command.", "type": "boolean"}}, "additionalProperties": false}]}}, "deny": {"items": {"title": "ShellScopeEntry", "description": "Shell scope entry.", "anyOf": [{"type": "object", "required": ["cmd", "name"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}}, "additionalProperties": false}, {"type": "object", "required": ["name", "sidecar"], "properties": {"args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "sidecar": {"description": "If this command is a sidecar command.", "type": "boolean"}}, "additionalProperties": false}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, "allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}}], "required": ["identifier"]}]}, "Identifier": {"description": "Permission identifier", "oneOf": [{"description": "Default core plugins set.\n#### This default permission set includes:\n\n- `core:path:default`\n- `core:event:default`\n- `core:window:default`\n- `core:webview:default`\n- `core:app:default`\n- `core:image:default`\n- `core:resources:default`\n- `core:menu:default`\n- `core:tray:default`", "type": "string", "const": "core:default", "markdownDescription": "Default core plugins set.\n#### This default permission set includes:\n\n- `core:path:default`\n- `core:event:default`\n- `core:window:default`\n- `core:webview:default`\n- `core:app:default`\n- `core:image:default`\n- `core:resources:default`\n- `core:menu:default`\n- `core:tray:default`"}, {"description": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-version`\n- `allow-name`\n- `allow-tauri-version`\n- `allow-identifier`\n- `allow-bundle-type`", "type": "string", "const": "core:app:default", "markdownDescription": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-version`\n- `allow-name`\n- `allow-tauri-version`\n- `allow-identifier`\n- `allow-bundle-type`"}, {"description": "Enables the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-hide", "markdownDescription": "Enables the app_hide command without any pre-configured scope."}, {"description": "Enables the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-show", "markdownDescription": "Enables the app_show command without any pre-configured scope."}, {"description": "Enables the bundle_type command without any pre-configured scope.", "type": "string", "const": "core:app:allow-bundle-type", "markdownDescription": "Enables the bundle_type command without any pre-configured scope."}, {"description": "Enables the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:allow-default-window-icon", "markdownDescription": "Enables the default_window_icon command without any pre-configured scope."}, {"description": "Enables the fetch_data_store_identifiers command without any pre-configured scope.", "type": "string", "const": "core:app:allow-fetch-data-store-identifiers", "markdownDescription": "Enables the fetch_data_store_identifiers command without any pre-configured scope."}, {"description": "Enables the identifier command without any pre-configured scope.", "type": "string", "const": "core:app:allow-identifier", "markdownDescription": "Enables the identifier command without any pre-configured scope."}, {"description": "Enables the name command without any pre-configured scope.", "type": "string", "const": "core:app:allow-name", "markdownDescription": "Enables the name command without any pre-configured scope."}, {"description": "Enables the remove_data_store command without any pre-configured scope.", "type": "string", "const": "core:app:allow-remove-data-store", "markdownDescription": "Enables the remove_data_store command without any pre-configured scope."}, {"description": "Enables the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:allow-set-app-theme", "markdownDescription": "Enables the set_app_theme command without any pre-configured scope."}, {"description": "Enables the set_dock_visibility command without any pre-configured scope.", "type": "string", "const": "core:app:allow-set-dock-visibility", "markdownDescription": "Enables the set_dock_visibility command without any pre-configured scope."}, {"description": "Enables the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-tauri-version", "markdownDescription": "Enables the tauri_version command without any pre-configured scope."}, {"description": "Enables the version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-version", "markdownDescription": "Enables the version command without any pre-configured scope."}, {"description": "Denies the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-hide", "markdownDescription": "Denies the app_hide command without any pre-configured scope."}, {"description": "Denies the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-show", "markdownDescription": "Denies the app_show command without any pre-configured scope."}, {"description": "Denies the bundle_type command without any pre-configured scope.", "type": "string", "const": "core:app:deny-bundle-type", "markdownDescription": "Denies the bundle_type command without any pre-configured scope."}, {"description": "Denies the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:deny-default-window-icon", "markdownDescription": "Denies the default_window_icon command without any pre-configured scope."}, {"description": "Denies the fetch_data_store_identifiers command without any pre-configured scope.", "type": "string", "const": "core:app:deny-fetch-data-store-identifiers", "markdownDescription": "Denies the fetch_data_store_identifiers command without any pre-configured scope."}, {"description": "Denies the identifier command without any pre-configured scope.", "type": "string", "const": "core:app:deny-identifier", "markdownDescription": "Denies the identifier command without any pre-configured scope."}, {"description": "Denies the name command without any pre-configured scope.", "type": "string", "const": "core:app:deny-name", "markdownDescription": "Denies the name command without any pre-configured scope."}, {"description": "Denies the remove_data_store command without any pre-configured scope.", "type": "string", "const": "core:app:deny-remove-data-store", "markdownDescription": "Denies the remove_data_store command without any pre-configured scope."}, {"description": "Denies the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:deny-set-app-theme", "markdownDescription": "Denies the set_app_theme command without any pre-configured scope."}, {"description": "Denies the set_dock_visibility command without any pre-configured scope.", "type": "string", "const": "core:app:deny-set-dock-visibility", "markdownDescription": "Denies the set_dock_visibility command without any pre-configured scope."}, {"description": "Denies the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-tauri-version", "markdownDescription": "Denies the tauri_version command without any pre-configured scope."}, {"description": "Denies the version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-version", "markdownDescription": "Denies the version command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-listen`\n- `allow-unlisten`\n- `allow-emit`\n- `allow-emit-to`", "type": "string", "const": "core:event:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-listen`\n- `allow-unlisten`\n- `allow-emit`\n- `allow-emit-to`"}, {"description": "Enables the emit command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit", "markdownDescription": "Enables the emit command without any pre-configured scope."}, {"description": "Enables the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit-to", "markdownDescription": "Enables the emit_to command without any pre-configured scope."}, {"description": "Enables the listen command without any pre-configured scope.", "type": "string", "const": "core:event:allow-listen", "markdownDescription": "Enables the listen command without any pre-configured scope."}, {"description": "Enables the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:allow-unlisten", "markdownDescription": "Enables the unlisten command without any pre-configured scope."}, {"description": "Denies the emit command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit", "markdownDescription": "Denies the emit command without any pre-configured scope."}, {"description": "Denies the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit-to", "markdownDescription": "Denies the emit_to command without any pre-configured scope."}, {"description": "Denies the listen command without any pre-configured scope.", "type": "string", "const": "core:event:deny-listen", "markdownDescription": "Denies the listen command without any pre-configured scope."}, {"description": "Denies the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:deny-unlisten", "markdownDescription": "Denies the unlisten command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-from-bytes`\n- `allow-from-path`\n- `allow-rgba`\n- `allow-size`", "type": "string", "const": "core:image:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-from-bytes`\n- `allow-from-path`\n- `allow-rgba`\n- `allow-size`"}, {"description": "Enables the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-bytes", "markdownDescription": "Enables the from_bytes command without any pre-configured scope."}, {"description": "Enables the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-path", "markdownDescription": "Enables the from_path command without any pre-configured scope."}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:image:allow-new", "markdownDescription": "Enables the new command without any pre-configured scope."}, {"description": "Enables the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:allow-rgba", "markdownDescription": "Enables the rgba command without any pre-configured scope."}, {"description": "Enables the size command without any pre-configured scope.", "type": "string", "const": "core:image:allow-size", "markdownDescription": "Enables the size command without any pre-configured scope."}, {"description": "Denies the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-bytes", "markdownDescription": "Denies the from_bytes command without any pre-configured scope."}, {"description": "Denies the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-path", "markdownDescription": "Denies the from_path command without any pre-configured scope."}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:image:deny-new", "markdownDescription": "Denies the new command without any pre-configured scope."}, {"description": "Denies the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:deny-rgba", "markdownDescription": "Denies the rgba command without any pre-configured scope."}, {"description": "Denies the size command without any pre-configured scope.", "type": "string", "const": "core:image:deny-size", "markdownDescription": "Denies the size command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-append`\n- `allow-prepend`\n- `allow-insert`\n- `allow-remove`\n- `allow-remove-at`\n- `allow-items`\n- `allow-get`\n- `allow-popup`\n- `allow-create-default`\n- `allow-set-as-app-menu`\n- `allow-set-as-window-menu`\n- `allow-text`\n- `allow-set-text`\n- `allow-is-enabled`\n- `allow-set-enabled`\n- `allow-set-accelerator`\n- `allow-set-as-windows-menu-for-nsapp`\n- `allow-set-as-help-menu-for-nsapp`\n- `allow-is-checked`\n- `allow-set-checked`\n- `allow-set-icon`", "type": "string", "const": "core:menu:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-append`\n- `allow-prepend`\n- `allow-insert`\n- `allow-remove`\n- `allow-remove-at`\n- `allow-items`\n- `allow-get`\n- `allow-popup`\n- `allow-create-default`\n- `allow-set-as-app-menu`\n- `allow-set-as-window-menu`\n- `allow-text`\n- `allow-set-text`\n- `allow-is-enabled`\n- `allow-set-enabled`\n- `allow-set-accelerator`\n- `allow-set-as-windows-menu-for-nsapp`\n- `allow-set-as-help-menu-for-nsapp`\n- `allow-is-checked`\n- `allow-set-checked`\n- `allow-set-icon`"}, {"description": "Enables the append command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-append", "markdownDescription": "Enables the append command without any pre-configured scope."}, {"description": "Enables the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-create-default", "markdownDescription": "Enables the create_default command without any pre-configured scope."}, {"description": "Enables the get command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-get", "markdownDescription": "Enables the get command without any pre-configured scope."}, {"description": "Enables the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-insert", "markdownDescription": "Enables the insert command without any pre-configured scope."}, {"description": "Enables the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-checked", "markdownDescription": "Enables the is_checked command without any pre-configured scope."}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-enabled", "markdownDescription": "Enables the is_enabled command without any pre-configured scope."}, {"description": "Enables the items command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-items", "markdownDescription": "Enables the items command without any pre-configured scope."}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-new", "markdownDescription": "Enables the new command without any pre-configured scope."}, {"description": "Enables the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-popup", "markdownDescription": "Enables the popup command without any pre-configured scope."}, {"description": "Enables the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-prepend", "markdownDescription": "Enables the prepend command without any pre-configured scope."}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove", "markdownDescription": "Enables the remove command without any pre-configured scope."}, {"description": "Enables the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove-at", "markdownDescription": "Enables the remove_at command without any pre-configured scope."}, {"description": "Enables the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-accelerator", "markdownDescription": "Enables the set_accelerator command without any pre-configured scope."}, {"description": "Enables the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-app-menu", "markdownDescription": "Enables the set_as_app_menu command without any pre-configured scope."}, {"description": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-help-menu-for-nsapp", "markdownDescription": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope."}, {"description": "Enables the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-window-menu", "markdownDescription": "Enables the set_as_window_menu command without any pre-configured scope."}, {"description": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-windows-menu-for-nsapp", "markdownDescription": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope."}, {"description": "Enables the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-checked", "markdownDescription": "Enables the set_checked command without any pre-configured scope."}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-enabled", "markdownDescription": "Enables the set_enabled command without any pre-configured scope."}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-icon", "markdownDescription": "Enables the set_icon command without any pre-configured scope."}, {"description": "Enables the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-text", "markdownDescription": "Enables the set_text command without any pre-configured scope."}, {"description": "Enables the text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-text", "markdownDescription": "Enables the text command without any pre-configured scope."}, {"description": "Denies the append command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-append", "markdownDescription": "Denies the append command without any pre-configured scope."}, {"description": "Denies the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-create-default", "markdownDescription": "Denies the create_default command without any pre-configured scope."}, {"description": "Denies the get command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-get", "markdownDescription": "Denies the get command without any pre-configured scope."}, {"description": "Denies the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-insert", "markdownDescription": "Denies the insert command without any pre-configured scope."}, {"description": "Denies the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-checked", "markdownDescription": "Denies the is_checked command without any pre-configured scope."}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-enabled", "markdownDescription": "Denies the is_enabled command without any pre-configured scope."}, {"description": "Denies the items command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-items", "markdownDescription": "Denies the items command without any pre-configured scope."}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-new", "markdownDescription": "Denies the new command without any pre-configured scope."}, {"description": "Denies the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-popup", "markdownDescription": "Denies the popup command without any pre-configured scope."}, {"description": "Denies the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-prepend", "markdownDescription": "Denies the prepend command without any pre-configured scope."}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove", "markdownDescription": "Denies the remove command without any pre-configured scope."}, {"description": "Denies the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove-at", "markdownDescription": "Denies the remove_at command without any pre-configured scope."}, {"description": "Denies the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-accelerator", "markdownDescription": "Denies the set_accelerator command without any pre-configured scope."}, {"description": "Denies the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-app-menu", "markdownDescription": "Denies the set_as_app_menu command without any pre-configured scope."}, {"description": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-help-menu-for-nsapp", "markdownDescription": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope."}, {"description": "Denies the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-window-menu", "markdownDescription": "Denies the set_as_window_menu command without any pre-configured scope."}, {"description": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-windows-menu-for-nsapp", "markdownDescription": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope."}, {"description": "Denies the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-checked", "markdownDescription": "Denies the set_checked command without any pre-configured scope."}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-enabled", "markdownDescription": "Denies the set_enabled command without any pre-configured scope."}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-icon", "markdownDescription": "Denies the set_icon command without any pre-configured scope."}, {"description": "Denies the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-text", "markdownDescription": "Denies the set_text command without any pre-configured scope."}, {"description": "Denies the text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-text", "markdownDescription": "Denies the text command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-resolve-directory`\n- `allow-resolve`\n- `allow-normalize`\n- `allow-join`\n- `allow-dirname`\n- `allow-extname`\n- `allow-basename`\n- `allow-is-absolute`", "type": "string", "const": "core:path:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-resolve-directory`\n- `allow-resolve`\n- `allow-normalize`\n- `allow-join`\n- `allow-dirname`\n- `allow-extname`\n- `allow-basename`\n- `allow-is-absolute`"}, {"description": "Enables the basename command without any pre-configured scope.", "type": "string", "const": "core:path:allow-basename", "markdownDescription": "Enables the basename command without any pre-configured scope."}, {"description": "Enables the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-dirname", "markdownDescription": "Enables the dirname command without any pre-configured scope."}, {"description": "Enables the extname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-extname", "markdownDescription": "Enables the extname command without any pre-configured scope."}, {"description": "Enables the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:allow-is-absolute", "markdownDescription": "Enables the is_absolute command without any pre-configured scope."}, {"description": "Enables the join command without any pre-configured scope.", "type": "string", "const": "core:path:allow-join", "markdownDescription": "Enables the join command without any pre-configured scope."}, {"description": "Enables the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:allow-normalize", "markdownDescription": "Enables the normalize command without any pre-configured scope."}, {"description": "Enables the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve", "markdownDescription": "Enables the resolve command without any pre-configured scope."}, {"description": "Enables the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve-directory", "markdownDescription": "Enables the resolve_directory command without any pre-configured scope."}, {"description": "Denies the basename command without any pre-configured scope.", "type": "string", "const": "core:path:deny-basename", "markdownDescription": "Denies the basename command without any pre-configured scope."}, {"description": "Denies the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-dirname", "markdownDescription": "Denies the dirname command without any pre-configured scope."}, {"description": "Denies the extname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-extname", "markdownDescription": "Denies the extname command without any pre-configured scope."}, {"description": "Denies the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:deny-is-absolute", "markdownDescription": "Denies the is_absolute command without any pre-configured scope."}, {"description": "Denies the join command without any pre-configured scope.", "type": "string", "const": "core:path:deny-join", "markdownDescription": "Denies the join command without any pre-configured scope."}, {"description": "Denies the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:deny-normalize", "markdownDescription": "Denies the normalize command without any pre-configured scope."}, {"description": "Denies the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve", "markdownDescription": "Denies the resolve command without any pre-configured scope."}, {"description": "Denies the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve-directory", "markdownDescription": "Denies the resolve_directory command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-close`", "type": "string", "const": "core:resources:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-close`"}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:resources:allow-close", "markdownDescription": "Enables the close command without any pre-configured scope."}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:resources:deny-close", "markdownDescription": "Denies the close command without any pre-configured scope."}, {"description": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-get-by-id`\n- `allow-remove-by-id`\n- `allow-set-icon`\n- `allow-set-menu`\n- `allow-set-tooltip`\n- `allow-set-title`\n- `allow-set-visible`\n- `allow-set-temp-dir-path`\n- `allow-set-icon-as-template`\n- `allow-set-show-menu-on-left-click`", "type": "string", "const": "core:tray:default", "markdownDescription": "Default permissions for the plugin, which enables all commands.\n#### This default permission set includes:\n\n- `allow-new`\n- `allow-get-by-id`\n- `allow-remove-by-id`\n- `allow-set-icon`\n- `allow-set-menu`\n- `allow-set-tooltip`\n- `allow-set-title`\n- `allow-set-visible`\n- `allow-set-temp-dir-path`\n- `allow-set-icon-as-template`\n- `allow-set-show-menu-on-left-click`"}, {"description": "Enables the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-get-by-id", "markdownDescription": "Enables the get_by_id command without any pre-configured scope."}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-new", "markdownDescription": "Enables the new command without any pre-configured scope."}, {"description": "Enables the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-remove-by-id", "markdownDescription": "Enables the remove_by_id command without any pre-configured scope."}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon", "markdownDescription": "Enables the set_icon command without any pre-configured scope."}, {"description": "Enables the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon-as-template", "markdownDescription": "Enables the set_icon_as_template command without any pre-configured scope."}, {"description": "Enables the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-menu", "markdownDescription": "Enables the set_menu command without any pre-configured scope."}, {"description": "Enables the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-show-menu-on-left-click", "markdownDescription": "Enables the set_show_menu_on_left_click command without any pre-configured scope."}, {"description": "Enables the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-temp-dir-path", "markdownDescription": "Enables the set_temp_dir_path command without any pre-configured scope."}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-title", "markdownDescription": "Enables the set_title command without any pre-configured scope."}, {"description": "Enables the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-tooltip", "markdownDescription": "Enables the set_tooltip command without any pre-configured scope."}, {"description": "Enables the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-visible", "markdownDescription": "Enables the set_visible command without any pre-configured scope."}, {"description": "Denies the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-get-by-id", "markdownDescription": "Denies the get_by_id command without any pre-configured scope."}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-new", "markdownDescription": "Denies the new command without any pre-configured scope."}, {"description": "Denies the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-remove-by-id", "markdownDescription": "Denies the remove_by_id command without any pre-configured scope."}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon", "markdownDescription": "Denies the set_icon command without any pre-configured scope."}, {"description": "Denies the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon-as-template", "markdownDescription": "Denies the set_icon_as_template command without any pre-configured scope."}, {"description": "Denies the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-menu", "markdownDescription": "Denies the set_menu command without any pre-configured scope."}, {"description": "Denies the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-show-menu-on-left-click", "markdownDescription": "Denies the set_show_menu_on_left_click command without any pre-configured scope."}, {"description": "Denies the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-temp-dir-path", "markdownDescription": "Denies the set_temp_dir_path command without any pre-configured scope."}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-title", "markdownDescription": "Denies the set_title command without any pre-configured scope."}, {"description": "Denies the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-tooltip", "markdownDescription": "Denies the set_tooltip command without any pre-configured scope."}, {"description": "Denies the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-visible", "markdownDescription": "Denies the set_visible command without any pre-configured scope."}, {"description": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-get-all-webviews`\n- `allow-webview-position`\n- `allow-webview-size`\n- `allow-internal-toggle-devtools`", "type": "string", "const": "core:webview:default", "markdownDescription": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-get-all-webviews`\n- `allow-webview-position`\n- `allow-webview-size`\n- `allow-internal-toggle-devtools`"}, {"description": "Enables the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-clear-all-browsing-data", "markdownDescription": "Enables the clear_all_browsing_data command without any pre-configured scope."}, {"description": "Enables the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview", "markdownDescription": "Enables the create_webview command without any pre-configured scope."}, {"description": "Enables the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview-window", "markdownDescription": "Enables the create_webview_window command without any pre-configured scope."}, {"description": "Enables the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-get-all-webviews", "markdownDescription": "Enables the get_all_webviews command without any pre-configured scope."}, {"description": "Enables the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-internal-toggle-devtools", "markdownDescription": "Enables the internal_toggle_devtools command without any pre-configured scope."}, {"description": "Enables the print command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-print", "markdownDescription": "Enables the print command without any pre-configured scope."}, {"description": "Enables the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-reparent", "markdownDescription": "Enables the reparent command without any pre-configured scope."}, {"description": "Enables the set_webview_auto_resize command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-auto-resize", "markdownDescription": "Enables the set_webview_auto_resize command without any pre-configured scope."}, {"description": "Enables the set_webview_background_color command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-background-color", "markdownDescription": "Enables the set_webview_background_color command without any pre-configured scope."}, {"description": "Enables the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-focus", "markdownDescription": "Enables the set_webview_focus command without any pre-configured scope."}, {"description": "Enables the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-position", "markdownDescription": "Enables the set_webview_position command without any pre-configured scope."}, {"description": "Enables the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-size", "markdownDescription": "Enables the set_webview_size command without any pre-configured scope."}, {"description": "Enables the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-zoom", "markdownDescription": "Enables the set_webview_zoom command without any pre-configured scope."}, {"description": "Enables the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-close", "markdownDescription": "Enables the webview_close command without any pre-configured scope."}, {"description": "Enables the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-hide", "markdownDescription": "Enables the webview_hide command without any pre-configured scope."}, {"description": "Enables the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-position", "markdownDescription": "Enables the webview_position command without any pre-configured scope."}, {"description": "Enables the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-show", "markdownDescription": "Enables the webview_show command without any pre-configured scope."}, {"description": "Enables the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-size", "markdownDescription": "Enables the webview_size command without any pre-configured scope."}, {"description": "Denies the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-clear-all-browsing-data", "markdownDescription": "Denies the clear_all_browsing_data command without any pre-configured scope."}, {"description": "Denies the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview", "markdownDescription": "Denies the create_webview command without any pre-configured scope."}, {"description": "Denies the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview-window", "markdownDescription": "Denies the create_webview_window command without any pre-configured scope."}, {"description": "Denies the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-get-all-webviews", "markdownDescription": "Denies the get_all_webviews command without any pre-configured scope."}, {"description": "Denies the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-internal-toggle-devtools", "markdownDescription": "Denies the internal_toggle_devtools command without any pre-configured scope."}, {"description": "Denies the print command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-print", "markdownDescription": "Denies the print command without any pre-configured scope."}, {"description": "Denies the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-reparent", "markdownDescription": "Denies the reparent command without any pre-configured scope."}, {"description": "Denies the set_webview_auto_resize command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-auto-resize", "markdownDescription": "Denies the set_webview_auto_resize command without any pre-configured scope."}, {"description": "Denies the set_webview_background_color command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-background-color", "markdownDescription": "Denies the set_webview_background_color command without any pre-configured scope."}, {"description": "Denies the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-focus", "markdownDescription": "Denies the set_webview_focus command without any pre-configured scope."}, {"description": "Denies the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-position", "markdownDescription": "Denies the set_webview_position command without any pre-configured scope."}, {"description": "Denies the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-size", "markdownDescription": "Denies the set_webview_size command without any pre-configured scope."}, {"description": "Denies the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-zoom", "markdownDescription": "Denies the set_webview_zoom command without any pre-configured scope."}, {"description": "Denies the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-close", "markdownDescription": "Denies the webview_close command without any pre-configured scope."}, {"description": "Denies the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-hide", "markdownDescription": "Denies the webview_hide command without any pre-configured scope."}, {"description": "Denies the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-position", "markdownDescription": "Denies the webview_position command without any pre-configured scope."}, {"description": "Denies the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-show", "markdownDescription": "Denies the webview_show command without any pre-configured scope."}, {"description": "Denies the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-size", "markdownDescription": "Denies the webview_size command without any pre-configured scope."}, {"description": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-get-all-windows`\n- `allow-scale-factor`\n- `allow-inner-position`\n- `allow-outer-position`\n- `allow-inner-size`\n- `allow-outer-size`\n- `allow-is-fullscreen`\n- `allow-is-minimized`\n- `allow-is-maximized`\n- `allow-is-focused`\n- `allow-is-decorated`\n- `allow-is-resizable`\n- `allow-is-maximizable`\n- `allow-is-minimizable`\n- `allow-is-closable`\n- `allow-is-visible`\n- `allow-is-enabled`\n- `allow-title`\n- `allow-current-monitor`\n- `allow-primary-monitor`\n- `allow-monitor-from-point`\n- `allow-available-monitors`\n- `allow-cursor-position`\n- `allow-theme`\n- `allow-is-always-on-top`\n- `allow-internal-toggle-maximize`", "type": "string", "const": "core:window:default", "markdownDescription": "Default permissions for the plugin.\n#### This default permission set includes:\n\n- `allow-get-all-windows`\n- `allow-scale-factor`\n- `allow-inner-position`\n- `allow-outer-position`\n- `allow-inner-size`\n- `allow-outer-size`\n- `allow-is-fullscreen`\n- `allow-is-minimized`\n- `allow-is-maximized`\n- `allow-is-focused`\n- `allow-is-decorated`\n- `allow-is-resizable`\n- `allow-is-maximizable`\n- `allow-is-minimizable`\n- `allow-is-closable`\n- `allow-is-visible`\n- `allow-is-enabled`\n- `allow-title`\n- `allow-current-monitor`\n- `allow-primary-monitor`\n- `allow-monitor-from-point`\n- `allow-available-monitors`\n- `allow-cursor-position`\n- `allow-theme`\n- `allow-is-always-on-top`\n- `allow-internal-toggle-maximize`"}, {"description": "Enables the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:allow-available-monitors", "markdownDescription": "Enables the available_monitors command without any pre-configured scope."}, {"description": "Enables the center command without any pre-configured scope.", "type": "string", "const": "core:window:allow-center", "markdownDescription": "Enables the center command without any pre-configured scope."}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:window:allow-close", "markdownDescription": "Enables the close command without any pre-configured scope."}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "core:window:allow-create", "markdownDescription": "Enables the create command without any pre-configured scope."}, {"description": "Enables the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-current-monitor", "markdownDescription": "Enables the current_monitor command without any pre-configured scope."}, {"description": "Enables the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-cursor-position", "markdownDescription": "Enables the cursor_position command without any pre-configured scope."}, {"description": "Enables the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:allow-destroy", "markdownDescription": "Enables the destroy command without any pre-configured scope."}, {"description": "Enables the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:allow-get-all-windows", "markdownDescription": "Enables the get_all_windows command without any pre-configured scope."}, {"description": "Enables the hide command without any pre-configured scope.", "type": "string", "const": "core:window:allow-hide", "markdownDescription": "Enables the hide command without any pre-configured scope."}, {"description": "Enables the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-position", "markdownDescription": "Enables the inner_position command without any pre-configured scope."}, {"description": "Enables the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-size", "markdownDescription": "Enables the inner_size command without any pre-configured scope."}, {"description": "Enables the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-internal-toggle-maximize", "markdownDescription": "Enables the internal_toggle_maximize command without any pre-configured scope."}, {"description": "Enables the is_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-always-on-top", "markdownDescription": "Enables the is_always_on_top command without any pre-configured scope."}, {"description": "Enables the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-closable", "markdownDescription": "Enables the is_closable command without any pre-configured scope."}, {"description": "Enables the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-decorated", "markdownDescription": "Enables the is_decorated command without any pre-configured scope."}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-enabled", "markdownDescription": "Enables the is_enabled command without any pre-configured scope."}, {"description": "Enables the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-focused", "markdownDescription": "Enables the is_focused command without any pre-configured scope."}, {"description": "Enables the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-fullscreen", "markdownDescription": "Enables the is_fullscreen command without any pre-configured scope."}, {"description": "Enables the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximizable", "markdownDescription": "Enables the is_maximizable command without any pre-configured scope."}, {"description": "Enables the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximized", "markdownDescription": "Enables the is_maximized command without any pre-configured scope."}, {"description": "Enables the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimizable", "markdownDescription": "Enables the is_minimizable command without any pre-configured scope."}, {"description": "Enables the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimized", "markdownDescription": "Enables the is_minimized command without any pre-configured scope."}, {"description": "Enables the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-resizable", "markdownDescription": "Enables the is_resizable command without any pre-configured scope."}, {"description": "Enables the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-visible", "markdownDescription": "Enables the is_visible command without any pre-configured scope."}, {"description": "Enables the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-maximize", "markdownDescription": "Enables the maximize command without any pre-configured scope."}, {"description": "Enables the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-minimize", "markdownDescription": "Enables the minimize command without any pre-configured scope."}, {"description": "Enables the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:allow-monitor-from-point", "markdownDescription": "Enables the monitor_from_point command without any pre-configured scope."}, {"description": "Enables the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-position", "markdownDescription": "Enables the outer_position command without any pre-configured scope."}, {"description": "Enables the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-size", "markdownDescription": "Enables the outer_size command without any pre-configured scope."}, {"description": "Enables the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-primary-monitor", "markdownDescription": "Enables the primary_monitor command without any pre-configured scope."}, {"description": "Enables the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:allow-request-user-attention", "markdownDescription": "Enables the request_user_attention command without any pre-configured scope."}, {"description": "Enables the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-scale-factor", "markdownDescription": "Enables the scale_factor command without any pre-configured scope."}, {"description": "Enables the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-bottom", "markdownDescription": "Enables the set_always_on_bottom command without any pre-configured scope."}, {"description": "Enables the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-top", "markdownDescription": "Enables the set_always_on_top command without any pre-configured scope."}, {"description": "Enables the set_background_color command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-background-color", "markdownDescription": "Enables the set_background_color command without any pre-configured scope."}, {"description": "Enables the set_badge_count command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-badge-count", "markdownDescription": "Enables the set_badge_count command without any pre-configured scope."}, {"description": "Enables the set_badge_label command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-badge-label", "markdownDescription": "Enables the set_badge_label command without any pre-configured scope."}, {"description": "Enables the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-closable", "markdownDescription": "Enables the set_closable command without any pre-configured scope."}, {"description": "Enables the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-content-protected", "markdownDescription": "Enables the set_content_protected command without any pre-configured scope."}, {"description": "Enables the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-grab", "markdownDescription": "Enables the set_cursor_grab command without any pre-configured scope."}, {"description": "Enables the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-icon", "markdownDescription": "Enables the set_cursor_icon command without any pre-configured scope."}, {"description": "Enables the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-position", "markdownDescription": "Enables the set_cursor_position command without any pre-configured scope."}, {"description": "Enables the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-visible", "markdownDescription": "Enables the set_cursor_visible command without any pre-configured scope."}, {"description": "Enables the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-decorations", "markdownDescription": "Enables the set_decorations command without any pre-configured scope."}, {"description": "Enables the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-effects", "markdownDescription": "Enables the set_effects command without any pre-configured scope."}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-enabled", "markdownDescription": "Enables the set_enabled command without any pre-configured scope."}, {"description": "Enables the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-focus", "markdownDescription": "Enables the set_focus command without any pre-configured scope."}, {"description": "Enables the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-fullscreen", "markdownDescription": "Enables the set_fullscreen command without any pre-configured scope."}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-icon", "markdownDescription": "Enables the set_icon command without any pre-configured scope."}, {"description": "Enables the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-ignore-cursor-events", "markdownDescription": "Enables the set_ignore_cursor_events command without any pre-configured scope."}, {"description": "Enables the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-max-size", "markdownDescription": "Enables the set_max_size command without any pre-configured scope."}, {"description": "Enables the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-maximizable", "markdownDescription": "Enables the set_maximizable command without any pre-configured scope."}, {"description": "Enables the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-min-size", "markdownDescription": "Enables the set_min_size command without any pre-configured scope."}, {"description": "Enables the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-minimizable", "markdownDescription": "Enables the set_minimizable command without any pre-configured scope."}, {"description": "Enables the set_overlay_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-overlay-icon", "markdownDescription": "Enables the set_overlay_icon command without any pre-configured scope."}, {"description": "Enables the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-position", "markdownDescription": "Enables the set_position command without any pre-configured scope."}, {"description": "Enables the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-progress-bar", "markdownDescription": "Enables the set_progress_bar command without any pre-configured scope."}, {"description": "Enables the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-resizable", "markdownDescription": "Enables the set_resizable command without any pre-configured scope."}, {"description": "Enables the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-shadow", "markdownDescription": "Enables the set_shadow command without any pre-configured scope."}, {"description": "Enables the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size", "markdownDescription": "Enables the set_size command without any pre-configured scope."}, {"description": "Enables the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size-constraints", "markdownDescription": "Enables the set_size_constraints command without any pre-configured scope."}, {"description": "Enables the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-skip-taskbar", "markdownDescription": "Enables the set_skip_taskbar command without any pre-configured scope."}, {"description": "Enables the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-theme", "markdownDescription": "Enables the set_theme command without any pre-configured scope."}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title", "markdownDescription": "Enables the set_title command without any pre-configured scope."}, {"description": "Enables the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title-bar-style", "markdownDescription": "Enables the set_title_bar_style command without any pre-configured scope."}, {"description": "Enables the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-visible-on-all-workspaces", "markdownDescription": "Enables the set_visible_on_all_workspaces command without any pre-configured scope."}, {"description": "Enables the show command without any pre-configured scope.", "type": "string", "const": "core:window:allow-show", "markdownDescription": "Enables the show command without any pre-configured scope."}, {"description": "Enables the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-dragging", "markdownDescription": "Enables the start_dragging command without any pre-configured scope."}, {"description": "Enables the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-resize-dragging", "markdownDescription": "Enables the start_resize_dragging command without any pre-configured scope."}, {"description": "Enables the theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-theme", "markdownDescription": "Enables the theme command without any pre-configured scope."}, {"description": "Enables the title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-title", "markdownDescription": "Enables the title command without any pre-configured scope."}, {"description": "Enables the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-toggle-maximize", "markdownDescription": "Enables the toggle_maximize command without any pre-configured scope."}, {"description": "Enables the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unmaximize", "markdownDescription": "Enables the unmaximize command without any pre-configured scope."}, {"description": "Enables the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unminimize", "markdownDescription": "Enables the unminimize command without any pre-configured scope."}, {"description": "Denies the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:deny-available-monitors", "markdownDescription": "Denies the available_monitors command without any pre-configured scope."}, {"description": "Denies the center command without any pre-configured scope.", "type": "string", "const": "core:window:deny-center", "markdownDescription": "Denies the center command without any pre-configured scope."}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:window:deny-close", "markdownDescription": "Denies the close command without any pre-configured scope."}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "core:window:deny-create", "markdownDescription": "Denies the create command without any pre-configured scope."}, {"description": "Denies the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-current-monitor", "markdownDescription": "Denies the current_monitor command without any pre-configured scope."}, {"description": "Denies the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-cursor-position", "markdownDescription": "Denies the cursor_position command without any pre-configured scope."}, {"description": "Denies the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:deny-destroy", "markdownDescription": "Denies the destroy command without any pre-configured scope."}, {"description": "Denies the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:deny-get-all-windows", "markdownDescription": "Denies the get_all_windows command without any pre-configured scope."}, {"description": "Denies the hide command without any pre-configured scope.", "type": "string", "const": "core:window:deny-hide", "markdownDescription": "Denies the hide command without any pre-configured scope."}, {"description": "Denies the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-position", "markdownDescription": "Denies the inner_position command without any pre-configured scope."}, {"description": "Denies the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-size", "markdownDescription": "Denies the inner_size command without any pre-configured scope."}, {"description": "Denies the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-internal-toggle-maximize", "markdownDescription": "Denies the internal_toggle_maximize command without any pre-configured scope."}, {"description": "Denies the is_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-always-on-top", "markdownDescription": "Denies the is_always_on_top command without any pre-configured scope."}, {"description": "Denies the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-closable", "markdownDescription": "Denies the is_closable command without any pre-configured scope."}, {"description": "Denies the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-decorated", "markdownDescription": "Denies the is_decorated command without any pre-configured scope."}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-enabled", "markdownDescription": "Denies the is_enabled command without any pre-configured scope."}, {"description": "Denies the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-focused", "markdownDescription": "Denies the is_focused command without any pre-configured scope."}, {"description": "Denies the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-fullscreen", "markdownDescription": "Denies the is_fullscreen command without any pre-configured scope."}, {"description": "Denies the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximizable", "markdownDescription": "Denies the is_maximizable command without any pre-configured scope."}, {"description": "Denies the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximized", "markdownDescription": "Denies the is_maximized command without any pre-configured scope."}, {"description": "Denies the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimizable", "markdownDescription": "Denies the is_minimizable command without any pre-configured scope."}, {"description": "Denies the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimized", "markdownDescription": "Denies the is_minimized command without any pre-configured scope."}, {"description": "Denies the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-resizable", "markdownDescription": "Denies the is_resizable command without any pre-configured scope."}, {"description": "Denies the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-visible", "markdownDescription": "Denies the is_visible command without any pre-configured scope."}, {"description": "Denies the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-maximize", "markdownDescription": "Denies the maximize command without any pre-configured scope."}, {"description": "Denies the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-minimize", "markdownDescription": "Denies the minimize command without any pre-configured scope."}, {"description": "Denies the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:deny-monitor-from-point", "markdownDescription": "Denies the monitor_from_point command without any pre-configured scope."}, {"description": "Denies the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-position", "markdownDescription": "Denies the outer_position command without any pre-configured scope."}, {"description": "Denies the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-size", "markdownDescription": "Denies the outer_size command without any pre-configured scope."}, {"description": "Denies the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-primary-monitor", "markdownDescription": "Denies the primary_monitor command without any pre-configured scope."}, {"description": "Denies the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:deny-request-user-attention", "markdownDescription": "Denies the request_user_attention command without any pre-configured scope."}, {"description": "Denies the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-scale-factor", "markdownDescription": "Denies the scale_factor command without any pre-configured scope."}, {"description": "Denies the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-bottom", "markdownDescription": "Denies the set_always_on_bottom command without any pre-configured scope."}, {"description": "Denies the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-top", "markdownDescription": "Denies the set_always_on_top command without any pre-configured scope."}, {"description": "Denies the set_background_color command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-background-color", "markdownDescription": "Denies the set_background_color command without any pre-configured scope."}, {"description": "Denies the set_badge_count command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-badge-count", "markdownDescription": "Denies the set_badge_count command without any pre-configured scope."}, {"description": "Denies the set_badge_label command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-badge-label", "markdownDescription": "Denies the set_badge_label command without any pre-configured scope."}, {"description": "Denies the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-closable", "markdownDescription": "Denies the set_closable command without any pre-configured scope."}, {"description": "Denies the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-content-protected", "markdownDescription": "Denies the set_content_protected command without any pre-configured scope."}, {"description": "Denies the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-grab", "markdownDescription": "Denies the set_cursor_grab command without any pre-configured scope."}, {"description": "Denies the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-icon", "markdownDescription": "Denies the set_cursor_icon command without any pre-configured scope."}, {"description": "Denies the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-position", "markdownDescription": "Denies the set_cursor_position command without any pre-configured scope."}, {"description": "Denies the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-visible", "markdownDescription": "Denies the set_cursor_visible command without any pre-configured scope."}, {"description": "Denies the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-decorations", "markdownDescription": "Denies the set_decorations command without any pre-configured scope."}, {"description": "Denies the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-effects", "markdownDescription": "Denies the set_effects command without any pre-configured scope."}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-enabled", "markdownDescription": "Denies the set_enabled command without any pre-configured scope."}, {"description": "Denies the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-focus", "markdownDescription": "Denies the set_focus command without any pre-configured scope."}, {"description": "Denies the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-fullscreen", "markdownDescription": "Denies the set_fullscreen command without any pre-configured scope."}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-icon", "markdownDescription": "Denies the set_icon command without any pre-configured scope."}, {"description": "Denies the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-ignore-cursor-events", "markdownDescription": "Denies the set_ignore_cursor_events command without any pre-configured scope."}, {"description": "Denies the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-max-size", "markdownDescription": "Denies the set_max_size command without any pre-configured scope."}, {"description": "Denies the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-maximizable", "markdownDescription": "Denies the set_maximizable command without any pre-configured scope."}, {"description": "Denies the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-min-size", "markdownDescription": "Denies the set_min_size command without any pre-configured scope."}, {"description": "Denies the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-minimizable", "markdownDescription": "Denies the set_minimizable command without any pre-configured scope."}, {"description": "Denies the set_overlay_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-overlay-icon", "markdownDescription": "Denies the set_overlay_icon command without any pre-configured scope."}, {"description": "Denies the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-position", "markdownDescription": "Denies the set_position command without any pre-configured scope."}, {"description": "Denies the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-progress-bar", "markdownDescription": "Denies the set_progress_bar command without any pre-configured scope."}, {"description": "Denies the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-resizable", "markdownDescription": "Denies the set_resizable command without any pre-configured scope."}, {"description": "Denies the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-shadow", "markdownDescription": "Denies the set_shadow command without any pre-configured scope."}, {"description": "Denies the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size", "markdownDescription": "Denies the set_size command without any pre-configured scope."}, {"description": "Denies the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size-constraints", "markdownDescription": "Denies the set_size_constraints command without any pre-configured scope."}, {"description": "Denies the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-skip-taskbar", "markdownDescription": "Denies the set_skip_taskbar command without any pre-configured scope."}, {"description": "Denies the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-theme", "markdownDescription": "Denies the set_theme command without any pre-configured scope."}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title", "markdownDescription": "Denies the set_title command without any pre-configured scope."}, {"description": "Denies the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title-bar-style", "markdownDescription": "Denies the set_title_bar_style command without any pre-configured scope."}, {"description": "Denies the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-visible-on-all-workspaces", "markdownDescription": "Denies the set_visible_on_all_workspaces command without any pre-configured scope."}, {"description": "Denies the show command without any pre-configured scope.", "type": "string", "const": "core:window:deny-show", "markdownDescription": "Denies the show command without any pre-configured scope."}, {"description": "Denies the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-dragging", "markdownDescription": "Denies the start_dragging command without any pre-configured scope."}, {"description": "Denies the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-resize-dragging", "markdownDescription": "Denies the start_resize_dragging command without any pre-configured scope."}, {"description": "Denies the theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-theme", "markdownDescription": "Denies the theme command without any pre-configured scope."}, {"description": "Denies the title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-title", "markdownDescription": "Denies the title command without any pre-configured scope."}, {"description": "Denies the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-toggle-maximize", "markdownDescription": "Denies the toggle_maximize command without any pre-configured scope."}, {"description": "Denies the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unmaximize", "markdownDescription": "Denies the unmaximize command without any pre-configured scope."}, {"description": "Denies the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unminimize", "markdownDescription": "Denies the unminimize command without any pre-configured scope."}, {"description": "This permission set configures the types of dialogs\navailable from the dialog plugin.\n\n#### Granted Permissions\n\nAll dialog types are enabled.\n\n\n\n#### This default permission set includes:\n\n- `allow-ask`\n- `allow-confirm`\n- `allow-message`\n- `allow-save`\n- `allow-open`", "type": "string", "const": "dialog:default", "markdownDescription": "This permission set configures the types of dialogs\navailable from the dialog plugin.\n\n#### Granted Permissions\n\nAll dialog types are enabled.\n\n\n\n#### This default permission set includes:\n\n- `allow-ask`\n- `allow-confirm`\n- `allow-message`\n- `allow-save`\n- `allow-open`"}, {"description": "Enables the ask command without any pre-configured scope.", "type": "string", "const": "dialog:allow-ask", "markdownDescription": "Enables the ask command without any pre-configured scope."}, {"description": "Enables the confirm command without any pre-configured scope.", "type": "string", "const": "dialog:allow-confirm", "markdownDescription": "Enables the confirm command without any pre-configured scope."}, {"description": "Enables the message command without any pre-configured scope.", "type": "string", "const": "dialog:allow-message", "markdownDescription": "Enables the message command without any pre-configured scope."}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "dialog:allow-open", "markdownDescription": "Enables the open command without any pre-configured scope."}, {"description": "Enables the save command without any pre-configured scope.", "type": "string", "const": "dialog:allow-save", "markdownDescription": "Enables the save command without any pre-configured scope."}, {"description": "Denies the ask command without any pre-configured scope.", "type": "string", "const": "dialog:deny-ask", "markdownDescription": "Denies the ask command without any pre-configured scope."}, {"description": "Denies the confirm command without any pre-configured scope.", "type": "string", "const": "dialog:deny-confirm", "markdownDescription": "Denies the confirm command without any pre-configured scope."}, {"description": "Denies the message command without any pre-configured scope.", "type": "string", "const": "dialog:deny-message", "markdownDescription": "Denies the message command without any pre-configured scope."}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "dialog:deny-open", "markdownDescription": "Denies the open command without any pre-configured scope."}, {"description": "Denies the save command without any pre-configured scope.", "type": "string", "const": "dialog:deny-save", "markdownDescription": "Denies the save command without any pre-configured scope."}, {"description": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n#### This default permission set includes:\n\n- `create-app-specific-dirs`\n- `read-app-specific-dirs-recursive`\n- `deny-default`", "type": "string", "const": "fs:default", "markdownDescription": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n#### This default permission set includes:\n\n- `create-app-specific-dirs`\n- `read-app-specific-dirs-recursive`\n- `deny-default`"}, {"description": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-index`", "type": "string", "const": "fs:allow-app-meta", "markdownDescription": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-index`"}, {"description": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive read access to the application folders.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app`", "type": "string", "const": "fs:allow-app-read", "markdownDescription": "This allows non-recursive read access to the application folders.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app`"}, {"description": "This allows full recursive read access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-read-recursive", "markdownDescription": "This allows full recursive read access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive write access to the application folders.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app`", "type": "string", "const": "fs:allow-app-write", "markdownDescription": "This allows non-recursive write access to the application folders.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app`"}, {"description": "This allows full recursive write access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app-recursive`", "type": "string", "const": "fs:allow-app-write-recursive", "markdownDescription": "This allows full recursive write access to the complete application folders, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-app-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-index`", "type": "string", "const": "fs:allow-appcache-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive read access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache`", "type": "string", "const": "fs:allow-appcache-read", "markdownDescription": "This allows non-recursive read access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache`"}, {"description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive write access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache`", "type": "string", "const": "fs:allow-appcache-write", "markdownDescription": "This allows non-recursive write access to the `$APPCACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache`"}, {"description": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache-recursive`", "type": "string", "const": "fs:allow-appcache-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appcache-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-index`", "type": "string", "const": "fs:allow-appconfig-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive read access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig`", "type": "string", "const": "fs:allow-appconfig-read", "markdownDescription": "This allows non-recursive read access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig`"}, {"description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive write access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig`", "type": "string", "const": "fs:allow-appconfig-write", "markdownDescription": "This allows non-recursive write access to the `$APPCONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig`"}, {"description": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig-recursive`", "type": "string", "const": "fs:allow-appconfig-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appconfig-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-index`", "type": "string", "const": "fs:allow-appdata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive read access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata`", "type": "string", "const": "fs:allow-appdata-read", "markdownDescription": "This allows non-recursive read access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata`"}, {"description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive write access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata`", "type": "string", "const": "fs:allow-appdata-write", "markdownDescription": "This allows non-recursive write access to the `$APPDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata`"}, {"description": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata-recursive`", "type": "string", "const": "fs:allow-appdata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-appdata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-index`", "type": "string", "const": "fs:allow-applocaldata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata`", "type": "string", "const": "fs:allow-applocaldata-read", "markdownDescription": "This allows non-recursive read access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata`"}, {"description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata`", "type": "string", "const": "fs:allow-applocaldata-write", "markdownDescription": "This allows non-recursive write access to the `$APPLOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata`"}, {"description": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata-recursive`", "type": "string", "const": "fs:allow-applocaldata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applocaldata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-index`", "type": "string", "const": "fs:allow-applog-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-index`"}, {"description": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive read access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog`", "type": "string", "const": "fs:allow-applog-read", "markdownDescription": "This allows non-recursive read access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog`"}, {"description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive write access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog`", "type": "string", "const": "fs:allow-applog-write", "markdownDescription": "This allows non-recursive write access to the `$APPLOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog`"}, {"description": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog-recursive`", "type": "string", "const": "fs:allow-applog-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-applog-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-index`", "type": "string", "const": "fs:allow-audio-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-index`"}, {"description": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive read access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio`", "type": "string", "const": "fs:allow-audio-read", "markdownDescription": "This allows non-recursive read access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio`"}, {"description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive write access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio`", "type": "string", "const": "fs:allow-audio-write", "markdownDescription": "This allows non-recursive write access to the `$AUDIO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio`"}, {"description": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio-recursive`", "type": "string", "const": "fs:allow-audio-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-audio-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-index`", "type": "string", "const": "fs:allow-cache-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-index`"}, {"description": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive read access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache`", "type": "string", "const": "fs:allow-cache-read", "markdownDescription": "This allows non-recursive read access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache`"}, {"description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive write access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache`", "type": "string", "const": "fs:allow-cache-write", "markdownDescription": "This allows non-recursive write access to the `$CACHE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache`"}, {"description": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache-recursive`", "type": "string", "const": "fs:allow-cache-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-cache-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-index`", "type": "string", "const": "fs:allow-config-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-index`"}, {"description": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive read access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config`", "type": "string", "const": "fs:allow-config-read", "markdownDescription": "This allows non-recursive read access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config`"}, {"description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive write access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config`", "type": "string", "const": "fs:allow-config-write", "markdownDescription": "This allows non-recursive write access to the `$CONFIG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config`"}, {"description": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config-recursive`", "type": "string", "const": "fs:allow-config-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-config-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-index`", "type": "string", "const": "fs:allow-data-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-index`"}, {"description": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive read access to the `$DATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data`", "type": "string", "const": "fs:allow-data-read", "markdownDescription": "This allows non-recursive read access to the `$DATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data`"}, {"description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive write access to the `$DATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data`", "type": "string", "const": "fs:allow-data-write", "markdownDescription": "This allows non-recursive write access to the `$DATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data`"}, {"description": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data-recursive`", "type": "string", "const": "fs:allow-data-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-data-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-index`", "type": "string", "const": "fs:allow-desktop-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-index`"}, {"description": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive read access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop`", "type": "string", "const": "fs:allow-desktop-read", "markdownDescription": "This allows non-recursive read access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop`"}, {"description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive write access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop`", "type": "string", "const": "fs:allow-desktop-write", "markdownDescription": "This allows non-recursive write access to the `$DESKTOP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop`"}, {"description": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop-recursive`", "type": "string", "const": "fs:allow-desktop-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-desktop-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-index`", "type": "string", "const": "fs:allow-document-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-index`"}, {"description": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive read access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document`", "type": "string", "const": "fs:allow-document-read", "markdownDescription": "This allows non-recursive read access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document`"}, {"description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive write access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document`", "type": "string", "const": "fs:allow-document-write", "markdownDescription": "This allows non-recursive write access to the `$DOCUMENT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document`"}, {"description": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document-recursive`", "type": "string", "const": "fs:allow-document-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-document-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-index`", "type": "string", "const": "fs:allow-download-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-index`"}, {"description": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive read access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download`", "type": "string", "const": "fs:allow-download-read", "markdownDescription": "This allows non-recursive read access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download`"}, {"description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive write access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download`", "type": "string", "const": "fs:allow-download-write", "markdownDescription": "This allows non-recursive write access to the `$DOWNLOAD` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download`"}, {"description": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download-recursive`", "type": "string", "const": "fs:allow-download-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-download-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-index`", "type": "string", "const": "fs:allow-exe-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-index`"}, {"description": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive read access to the `$EXE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe`", "type": "string", "const": "fs:allow-exe-read", "markdownDescription": "This allows non-recursive read access to the `$EXE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe`"}, {"description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive write access to the `$EXE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe`", "type": "string", "const": "fs:allow-exe-write", "markdownDescription": "This allows non-recursive write access to the `$EXE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe`"}, {"description": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe-recursive`", "type": "string", "const": "fs:allow-exe-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-exe-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-index`", "type": "string", "const": "fs:allow-font-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-index`"}, {"description": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive read access to the `$FONT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font`", "type": "string", "const": "fs:allow-font-read", "markdownDescription": "This allows non-recursive read access to the `$FONT` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font`"}, {"description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive write access to the `$FONT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font`", "type": "string", "const": "fs:allow-font-write", "markdownDescription": "This allows non-recursive write access to the `$FONT` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font`"}, {"description": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font-recursive`", "type": "string", "const": "fs:allow-font-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-font-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-index`", "type": "string", "const": "fs:allow-home-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-index`"}, {"description": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive read access to the `$HOME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home`", "type": "string", "const": "fs:allow-home-read", "markdownDescription": "This allows non-recursive read access to the `$HOME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home`"}, {"description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive write access to the `$HOME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home`", "type": "string", "const": "fs:allow-home-write", "markdownDescription": "This allows non-recursive write access to the `$HOME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home`"}, {"description": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home-recursive`", "type": "string", "const": "fs:allow-home-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-home-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-index`", "type": "string", "const": "fs:allow-localdata-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-index`"}, {"description": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive read access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata`", "type": "string", "const": "fs:allow-localdata-read", "markdownDescription": "This allows non-recursive read access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata`"}, {"description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive write access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata`", "type": "string", "const": "fs:allow-localdata-write", "markdownDescription": "This allows non-recursive write access to the `$LOCALDATA` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata`"}, {"description": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata-recursive`", "type": "string", "const": "fs:allow-localdata-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-localdata-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-index`", "type": "string", "const": "fs:allow-log-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-index`"}, {"description": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive read access to the `$LOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log`", "type": "string", "const": "fs:allow-log-read", "markdownDescription": "This allows non-recursive read access to the `$LOG` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log`"}, {"description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive write access to the `$LOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log`", "type": "string", "const": "fs:allow-log-write", "markdownDescription": "This allows non-recursive write access to the `$LOG` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log`"}, {"description": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log-recursive`", "type": "string", "const": "fs:allow-log-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-log-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-index`", "type": "string", "const": "fs:allow-picture-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-index`"}, {"description": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive read access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture`", "type": "string", "const": "fs:allow-picture-read", "markdownDescription": "This allows non-recursive read access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture`"}, {"description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive write access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture`", "type": "string", "const": "fs:allow-picture-write", "markdownDescription": "This allows non-recursive write access to the `$PICTURE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture`"}, {"description": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture-recursive`", "type": "string", "const": "fs:allow-picture-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-picture-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-index`", "type": "string", "const": "fs:allow-public-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-index`"}, {"description": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive read access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public`", "type": "string", "const": "fs:allow-public-read", "markdownDescription": "This allows non-recursive read access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public`"}, {"description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive write access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public`", "type": "string", "const": "fs:allow-public-write", "markdownDescription": "This allows non-recursive write access to the `$PUBLIC` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public`"}, {"description": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public-recursive`", "type": "string", "const": "fs:allow-public-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-public-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-index`", "type": "string", "const": "fs:allow-resource-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-index`"}, {"description": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive read access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource`", "type": "string", "const": "fs:allow-resource-read", "markdownDescription": "This allows non-recursive read access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource`"}, {"description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive write access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource`", "type": "string", "const": "fs:allow-resource-write", "markdownDescription": "This allows non-recursive write access to the `$RESOURCE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource`"}, {"description": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource-recursive`", "type": "string", "const": "fs:allow-resource-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-resource-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-index`", "type": "string", "const": "fs:allow-runtime-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-index`"}, {"description": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive read access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime`", "type": "string", "const": "fs:allow-runtime-read", "markdownDescription": "This allows non-recursive read access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime`"}, {"description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive write access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime`", "type": "string", "const": "fs:allow-runtime-write", "markdownDescription": "This allows non-recursive write access to the `$RUNTIME` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime`"}, {"description": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime-recursive`", "type": "string", "const": "fs:allow-runtime-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-runtime-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-index`", "type": "string", "const": "fs:allow-temp-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-index`"}, {"description": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive read access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp`", "type": "string", "const": "fs:allow-temp-read", "markdownDescription": "This allows non-recursive read access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp`"}, {"description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive write access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp`", "type": "string", "const": "fs:allow-temp-write", "markdownDescription": "This allows non-recursive write access to the `$TEMP` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp`"}, {"description": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp-recursive`", "type": "string", "const": "fs:allow-temp-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-temp-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-index`", "type": "string", "const": "fs:allow-template-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-index`"}, {"description": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive read access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template`", "type": "string", "const": "fs:allow-template-read", "markdownDescription": "This allows non-recursive read access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template`"}, {"description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive write access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template`", "type": "string", "const": "fs:allow-template-write", "markdownDescription": "This allows non-recursive write access to the `$TEMPLATE` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template`"}, {"description": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template-recursive`", "type": "string", "const": "fs:allow-template-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-template-recursive`"}, {"description": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-index`", "type": "string", "const": "fs:allow-video-meta", "markdownDescription": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-index`"}, {"description": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-meta-recursive", "markdownDescription": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.\n#### This permission set includes:\n\n- `read-meta`\n- `scope-video-recursive`"}, {"description": "This allows non-recursive read access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video`", "type": "string", "const": "fs:allow-video-read", "markdownDescription": "This allows non-recursive read access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video`"}, {"description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-read-recursive", "markdownDescription": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `read-all`\n- `scope-video-recursive`"}, {"description": "This allows non-recursive write access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video`", "type": "string", "const": "fs:allow-video-write", "markdownDescription": "This allows non-recursive write access to the `$VIDEO` folder.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video`"}, {"description": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video-recursive`", "type": "string", "const": "fs:allow-video-write-recursive", "markdownDescription": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.\n#### This permission set includes:\n\n- `write-all`\n- `scope-video-recursive`"}, {"description": "This denies access to dangerous Tauri relevant files and folders by default.\n#### This permission set includes:\n\n- `deny-webview-data-linux`\n- `deny-webview-data-windows`", "type": "string", "const": "fs:deny-default", "markdownDescription": "This denies access to dangerous Tauri relevant files and folders by default.\n#### This permission set includes:\n\n- `deny-webview-data-linux`\n- `deny-webview-data-windows`"}, {"description": "Enables the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-copy-file", "markdownDescription": "Enables the copy_file command without any pre-configured scope."}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "fs:allow-create", "markdownDescription": "Enables the create command without any pre-configured scope."}, {"description": "Enables the exists command without any pre-configured scope.", "type": "string", "const": "fs:allow-exists", "markdownDescription": "Enables the exists command without any pre-configured scope."}, {"description": "Enables the fstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-fstat", "markdownDescription": "Enables the fstat command without any pre-configured scope."}, {"description": "Enables the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-ftruncate", "markdownDescription": "Enables the ftruncate command without any pre-configured scope."}, {"description": "Enables the lstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-lstat", "markdownDescription": "Enables the lstat command without any pre-configured scope."}, {"description": "Enables the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:allow-mkdir", "markdownDescription": "Enables the mkdir command without any pre-configured scope."}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "fs:allow-open", "markdownDescription": "Enables the open command without any pre-configured scope."}, {"description": "Enables the read command without any pre-configured scope.", "type": "string", "const": "fs:allow-read", "markdownDescription": "Enables the read command without any pre-configured scope."}, {"description": "Enables the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-dir", "markdownDescription": "Enables the read_dir command without any pre-configured scope."}, {"description": "Enables the read_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-file", "markdownDescription": "Enables the read_file command without any pre-configured scope."}, {"description": "Enables the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file", "markdownDescription": "Enables the read_text_file command without any pre-configured scope."}, {"description": "Enables the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines", "markdownDescription": "Enables the read_text_file_lines command without any pre-configured scope."}, {"description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines-next", "markdownDescription": "Enables the read_text_file_lines_next command without any pre-configured scope."}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "fs:allow-remove", "markdownDescription": "Enables the remove command without any pre-configured scope."}, {"description": "Enables the rename command without any pre-configured scope.", "type": "string", "const": "fs:allow-rename", "markdownDescription": "Enables the rename command without any pre-configured scope."}, {"description": "Enables the seek command without any pre-configured scope.", "type": "string", "const": "fs:allow-seek", "markdownDescription": "Enables the seek command without any pre-configured scope."}, {"description": "Enables the size command without any pre-configured scope.", "type": "string", "const": "fs:allow-size", "markdownDescription": "Enables the size command without any pre-configured scope."}, {"description": "Enables the stat command without any pre-configured scope.", "type": "string", "const": "fs:allow-stat", "markdownDescription": "Enables the stat command without any pre-configured scope."}, {"description": "Enables the truncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-truncate", "markdownDescription": "Enables the truncate command without any pre-configured scope."}, {"description": "Enables the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:allow-unwatch", "markdownDescription": "Enables the unwatch command without any pre-configured scope."}, {"description": "Enables the watch command without any pre-configured scope.", "type": "string", "const": "fs:allow-watch", "markdownDescription": "Enables the watch command without any pre-configured scope."}, {"description": "Enables the write command without any pre-configured scope.", "type": "string", "const": "fs:allow-write", "markdownDescription": "Enables the write command without any pre-configured scope."}, {"description": "Enables the write_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-file", "markdownDescription": "Enables the write_file command without any pre-configured scope."}, {"description": "Enables the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-text-file", "markdownDescription": "Enables the write_text_file command without any pre-configured scope."}, {"description": "This permissions allows to create the application specific directories.\n", "type": "string", "const": "fs:create-app-specific-dirs", "markdownDescription": "This permissions allows to create the application specific directories.\n"}, {"description": "Denies the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-copy-file", "markdownDescription": "Denies the copy_file command without any pre-configured scope."}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "fs:deny-create", "markdownDescription": "Denies the create command without any pre-configured scope."}, {"description": "Denies the exists command without any pre-configured scope.", "type": "string", "const": "fs:deny-exists", "markdownDescription": "Denies the exists command without any pre-configured scope."}, {"description": "Denies the fstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-fstat", "markdownDescription": "Denies the fstat command without any pre-configured scope."}, {"description": "Denies the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-ftruncate", "markdownDescription": "Denies the ftruncate command without any pre-configured scope."}, {"description": "Denies the lstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-lstat", "markdownDescription": "Denies the lstat command without any pre-configured scope."}, {"description": "Denies the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:deny-mkdir", "markdownDescription": "Denies the mkdir command without any pre-configured scope."}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "fs:deny-open", "markdownDescription": "Denies the open command without any pre-configured scope."}, {"description": "Denies the read command without any pre-configured scope.", "type": "string", "const": "fs:deny-read", "markdownDescription": "Denies the read command without any pre-configured scope."}, {"description": "Denies the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-dir", "markdownDescription": "Denies the read_dir command without any pre-configured scope."}, {"description": "Denies the read_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-file", "markdownDescription": "Denies the read_file command without any pre-configured scope."}, {"description": "Denies the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file", "markdownDescription": "Denies the read_text_file command without any pre-configured scope."}, {"description": "Denies the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines", "markdownDescription": "Denies the read_text_file_lines command without any pre-configured scope."}, {"description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines-next", "markdownDescription": "Denies the read_text_file_lines_next command without any pre-configured scope."}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "fs:deny-remove", "markdownDescription": "Denies the remove command without any pre-configured scope."}, {"description": "Denies the rename command without any pre-configured scope.", "type": "string", "const": "fs:deny-rename", "markdownDescription": "Denies the rename command without any pre-configured scope."}, {"description": "Denies the seek command without any pre-configured scope.", "type": "string", "const": "fs:deny-seek", "markdownDescription": "Denies the seek command without any pre-configured scope."}, {"description": "Denies the size command without any pre-configured scope.", "type": "string", "const": "fs:deny-size", "markdownDescription": "Denies the size command without any pre-configured scope."}, {"description": "Denies the stat command without any pre-configured scope.", "type": "string", "const": "fs:deny-stat", "markdownDescription": "Denies the stat command without any pre-configured scope."}, {"description": "Denies the truncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-truncate", "markdownDescription": "Denies the truncate command without any pre-configured scope."}, {"description": "Denies the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:deny-unwatch", "markdownDescription": "Denies the unwatch command without any pre-configured scope."}, {"description": "Denies the watch command without any pre-configured scope.", "type": "string", "const": "fs:deny-watch", "markdownDescription": "Denies the watch command without any pre-configured scope."}, {"description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-linux", "markdownDescription": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered."}, {"description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-windows", "markdownDescription": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered."}, {"description": "Denies the write command without any pre-configured scope.", "type": "string", "const": "fs:deny-write", "markdownDescription": "Denies the write command without any pre-configured scope."}, {"description": "Denies the write_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-file", "markdownDescription": "Denies the write_file command without any pre-configured scope."}, {"description": "Denies the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-text-file", "markdownDescription": "Denies the write_text_file command without any pre-configured scope."}, {"description": "This enables all read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-all", "markdownDescription": "This enables all read related commands without any pre-configured accessible paths."}, {"description": "This permission allows recursive read functionality on the application\nspecific base directories. \n", "type": "string", "const": "fs:read-app-specific-dirs-recursive", "markdownDescription": "This permission allows recursive read functionality on the application\nspecific base directories. \n"}, {"description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-dirs", "markdownDescription": "This enables directory read and file metadata related commands without any pre-configured accessible paths."}, {"description": "This enables file read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-files", "markdownDescription": "This enables file read related commands without any pre-configured accessible paths."}, {"description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-meta", "markdownDescription": "This enables all index or metadata related commands without any pre-configured accessible paths."}, {"description": "An empty permission you can use to modify the global scope.", "type": "string", "const": "fs:scope", "markdownDescription": "An empty permission you can use to modify the global scope."}, {"description": "This scope permits access to all files and list content of top level directories in the application folders.", "type": "string", "const": "fs:scope-app", "markdownDescription": "This scope permits access to all files and list content of top level directories in the application folders."}, {"description": "This scope permits to list all files and folders in the application directories.", "type": "string", "const": "fs:scope-app-index", "markdownDescription": "This scope permits to list all files and folders in the application directories."}, {"description": "This scope permits recursive access to the complete application folders, including sub directories and files.", "type": "string", "const": "fs:scope-app-recursive", "markdownDescription": "This scope permits recursive access to the complete application folders, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder.", "type": "string", "const": "fs:scope-appcache", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder."}, {"description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "type": "string", "const": "fs:scope-appcache-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPCACHE`folder."}, {"description": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appcache-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder.", "type": "string", "const": "fs:scope-appconfig", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder."}, {"description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "type": "string", "const": "fs:scope-appconfig-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPCONFIG`folder."}, {"description": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appconfig-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder.", "type": "string", "const": "fs:scope-appdata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "type": "string", "const": "fs:scope-appdata-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPDATA`folder."}, {"description": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appdata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:scope-applocaldata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "type": "string", "const": "fs:scope-applocaldata-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder."}, {"description": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applocaldata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder.", "type": "string", "const": "fs:scope-applog", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder."}, {"description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "type": "string", "const": "fs:scope-applog-index", "markdownDescription": "This scope permits to list all files and folders in the `$APPLOG`folder."}, {"description": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applog-recursive", "markdownDescription": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder.", "type": "string", "const": "fs:scope-audio", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder."}, {"description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "type": "string", "const": "fs:scope-audio-index", "markdownDescription": "This scope permits to list all files and folders in the `$AUDIO`folder."}, {"description": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-audio-recursive", "markdownDescription": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder.", "type": "string", "const": "fs:scope-cache", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder."}, {"description": "This scope permits to list all files and folders in the `$CACHE`folder.", "type": "string", "const": "fs:scope-cache-index", "markdownDescription": "This scope permits to list all files and folders in the `$CACHE`folder."}, {"description": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-cache-recursive", "markdownDescription": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder.", "type": "string", "const": "fs:scope-config", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder."}, {"description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "type": "string", "const": "fs:scope-config-index", "markdownDescription": "This scope permits to list all files and folders in the `$CONFIG`folder."}, {"description": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-config-recursive", "markdownDescription": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DATA` folder.", "type": "string", "const": "fs:scope-data", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DATA` folder."}, {"description": "This scope permits to list all files and folders in the `$DATA`folder.", "type": "string", "const": "fs:scope-data-index", "markdownDescription": "This scope permits to list all files and folders in the `$DATA`folder."}, {"description": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-data-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder.", "type": "string", "const": "fs:scope-desktop", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder."}, {"description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "type": "string", "const": "fs:scope-desktop-index", "markdownDescription": "This scope permits to list all files and folders in the `$DESKTOP`folder."}, {"description": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-desktop-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder.", "type": "string", "const": "fs:scope-document", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder."}, {"description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "type": "string", "const": "fs:scope-document-index", "markdownDescription": "This scope permits to list all files and folders in the `$DOCUMENT`folder."}, {"description": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-document-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder.", "type": "string", "const": "fs:scope-download", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder."}, {"description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "type": "string", "const": "fs:scope-download-index", "markdownDescription": "This scope permits to list all files and folders in the `$DOWNLOAD`folder."}, {"description": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "type": "string", "const": "fs:scope-download-recursive", "markdownDescription": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$EXE` folder.", "type": "string", "const": "fs:scope-exe", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$EXE` folder."}, {"description": "This scope permits to list all files and folders in the `$EXE`folder.", "type": "string", "const": "fs:scope-exe-index", "markdownDescription": "This scope permits to list all files and folders in the `$EXE`folder."}, {"description": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-exe-recursive", "markdownDescription": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$FONT` folder.", "type": "string", "const": "fs:scope-font", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$FONT` folder."}, {"description": "This scope permits to list all files and folders in the `$FONT`folder.", "type": "string", "const": "fs:scope-font-index", "markdownDescription": "This scope permits to list all files and folders in the `$FONT`folder."}, {"description": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-font-recursive", "markdownDescription": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$HOME` folder.", "type": "string", "const": "fs:scope-home", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$HOME` folder."}, {"description": "This scope permits to list all files and folders in the `$HOME`folder.", "type": "string", "const": "fs:scope-home-index", "markdownDescription": "This scope permits to list all files and folders in the `$HOME`folder."}, {"description": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-home-recursive", "markdownDescription": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder.", "type": "string", "const": "fs:scope-localdata", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder."}, {"description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "type": "string", "const": "fs:scope-localdata-index", "markdownDescription": "This scope permits to list all files and folders in the `$LOCALDATA`folder."}, {"description": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-localdata-recursive", "markdownDescription": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOG` folder.", "type": "string", "const": "fs:scope-log", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$LOG` folder."}, {"description": "This scope permits to list all files and folders in the `$LOG`folder.", "type": "string", "const": "fs:scope-log-index", "markdownDescription": "This scope permits to list all files and folders in the `$LOG`folder."}, {"description": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-log-recursive", "markdownDescription": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder.", "type": "string", "const": "fs:scope-picture", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder."}, {"description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "type": "string", "const": "fs:scope-picture-index", "markdownDescription": "This scope permits to list all files and folders in the `$PICTURE`folder."}, {"description": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-picture-recursive", "markdownDescription": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder.", "type": "string", "const": "fs:scope-public", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder."}, {"description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "type": "string", "const": "fs:scope-public-index", "markdownDescription": "This scope permits to list all files and folders in the `$PUBLIC`folder."}, {"description": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "type": "string", "const": "fs:scope-public-recursive", "markdownDescription": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder.", "type": "string", "const": "fs:scope-resource", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder."}, {"description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "type": "string", "const": "fs:scope-resource-index", "markdownDescription": "This scope permits to list all files and folders in the `$RESOURCE`folder."}, {"description": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-resource-recursive", "markdownDescription": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder.", "type": "string", "const": "fs:scope-runtime", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder."}, {"description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "type": "string", "const": "fs:scope-runtime-index", "markdownDescription": "This scope permits to list all files and folders in the `$RUNTIME`folder."}, {"description": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-runtime-recursive", "markdownDescription": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder.", "type": "string", "const": "fs:scope-temp", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder."}, {"description": "This scope permits to list all files and folders in the `$TEMP`folder.", "type": "string", "const": "fs:scope-temp-index", "markdownDescription": "This scope permits to list all files and folders in the `$TEMP`folder."}, {"description": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-temp-recursive", "markdownDescription": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder.", "type": "string", "const": "fs:scope-template", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder."}, {"description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "type": "string", "const": "fs:scope-template-index", "markdownDescription": "This scope permits to list all files and folders in the `$TEMPLATE`folder."}, {"description": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-template-recursive", "markdownDescription": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files."}, {"description": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder.", "type": "string", "const": "fs:scope-video", "markdownDescription": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder."}, {"description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "type": "string", "const": "fs:scope-video-index", "markdownDescription": "This scope permits to list all files and folders in the `$VIDEO`folder."}, {"description": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-video-recursive", "markdownDescription": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files."}, {"description": "This enables all write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-all", "markdownDescription": "This enables all write related commands without any pre-configured accessible paths."}, {"description": "This enables all file write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-files", "markdownDescription": "This enables all file write related commands without any pre-configured accessible paths."}, {"description": "Allows the log command\n#### This default permission set includes:\n\n- `allow-log`", "type": "string", "const": "log:default", "markdownDescription": "Allows the log command\n#### This default permission set includes:\n\n- `allow-log`"}, {"description": "Enables the log command without any pre-configured scope.", "type": "string", "const": "log:allow-log", "markdownDescription": "Enables the log command without any pre-configured scope."}, {"description": "Denies the log command without any pre-configured scope.", "type": "string", "const": "log:deny-log", "markdownDescription": "Denies the log command without any pre-configured scope."}, {"description": "This permission set allows opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application\nas well as reveal file in directories using default file explorer\n#### This default permission set includes:\n\n- `allow-open-url`\n- `allow-reveal-item-in-dir`\n- `allow-default-urls`", "type": "string", "const": "opener:default", "markdownDescription": "This permission set allows opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application\nas well as reveal file in directories using default file explorer\n#### This default permission set includes:\n\n- `allow-open-url`\n- `allow-reveal-item-in-dir`\n- `allow-default-urls`"}, {"description": "This enables opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application.", "type": "string", "const": "opener:allow-default-urls", "markdownDescription": "This enables opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application."}, {"description": "Enables the open_path command without any pre-configured scope.", "type": "string", "const": "opener:allow-open-path", "markdownDescription": "Enables the open_path command without any pre-configured scope."}, {"description": "Enables the open_url command without any pre-configured scope.", "type": "string", "const": "opener:allow-open-url", "markdownDescription": "Enables the open_url command without any pre-configured scope."}, {"description": "Enables the reveal_item_in_dir command without any pre-configured scope.", "type": "string", "const": "opener:allow-reveal-item-in-dir", "markdownDescription": "Enables the reveal_item_in_dir command without any pre-configured scope."}, {"description": "Denies the open_path command without any pre-configured scope.", "type": "string", "const": "opener:deny-open-path", "markdownDescription": "Denies the open_path command without any pre-configured scope."}, {"description": "Denies the open_url command without any pre-configured scope.", "type": "string", "const": "opener:deny-open-url", "markdownDescription": "Denies the open_url command without any pre-configured scope."}, {"description": "Denies the reveal_item_in_dir command without any pre-configured scope.", "type": "string", "const": "opener:deny-reveal-item-in-dir", "markdownDescription": "Denies the reveal_item_in_dir command without any pre-configured scope."}, {"description": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality with a reasonable\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n\n#### This default permission set includes:\n\n- `allow-open`", "type": "string", "const": "shell:default", "markdownDescription": "This permission set configures which\nshell functionality is exposed by default.\n\n#### Granted Permissions\n\nIt allows to use the `open` functionality with a reasonable\nscope pre-configured. It will allow opening `http(s)://`,\n`tel:` and `mailto:` links.\n\n#### This default permission set includes:\n\n- `allow-open`"}, {"description": "Enables the execute command without any pre-configured scope.", "type": "string", "const": "shell:allow-execute", "markdownDescription": "Enables the execute command without any pre-configured scope."}, {"description": "Enables the kill command without any pre-configured scope.", "type": "string", "const": "shell:allow-kill", "markdownDescription": "Enables the kill command without any pre-configured scope."}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "shell:allow-open", "markdownDescription": "Enables the open command without any pre-configured scope."}, {"description": "Enables the spawn command without any pre-configured scope.", "type": "string", "const": "shell:allow-spawn", "markdownDescription": "Enables the spawn command without any pre-configured scope."}, {"description": "Enables the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:allow-stdin-write", "markdownDescription": "Enables the stdin_write command without any pre-configured scope."}, {"description": "Denies the execute command without any pre-configured scope.", "type": "string", "const": "shell:deny-execute", "markdownDescription": "Denies the execute command without any pre-configured scope."}, {"description": "Denies the kill command without any pre-configured scope.", "type": "string", "const": "shell:deny-kill", "markdownDescription": "Denies the kill command without any pre-configured scope."}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "shell:deny-open", "markdownDescription": "Denies the open command without any pre-configured scope."}, {"description": "Denies the spawn command without any pre-configured scope.", "type": "string", "const": "shell:deny-spawn", "markdownDescription": "Denies the spawn command without any pre-configured scope."}, {"description": "Denies the stdin_write command without any pre-configured scope.", "type": "string", "const": "shell:deny-stdin-write", "markdownDescription": "Denies the stdin_write command without any pre-configured scope."}, {"description": "This permission set configures what kind of\noperations are available from the store plugin.\n\n#### Granted Permissions\n\nAll operations are enabled by default.\n\n\n#### This default permission set includes:\n\n- `allow-load`\n- `allow-get-store`\n- `allow-set`\n- `allow-get`\n- `allow-has`\n- `allow-delete`\n- `allow-clear`\n- `allow-reset`\n- `allow-keys`\n- `allow-values`\n- `allow-entries`\n- `allow-length`\n- `allow-reload`\n- `allow-save`", "type": "string", "const": "store:default", "markdownDescription": "This permission set configures what kind of\noperations are available from the store plugin.\n\n#### Granted Permissions\n\nAll operations are enabled by default.\n\n\n#### This default permission set includes:\n\n- `allow-load`\n- `allow-get-store`\n- `allow-set`\n- `allow-get`\n- `allow-has`\n- `allow-delete`\n- `allow-clear`\n- `allow-reset`\n- `allow-keys`\n- `allow-values`\n- `allow-entries`\n- `allow-length`\n- `allow-reload`\n- `allow-save`"}, {"description": "Enables the clear command without any pre-configured scope.", "type": "string", "const": "store:allow-clear", "markdownDescription": "Enables the clear command without any pre-configured scope."}, {"description": "Enables the delete command without any pre-configured scope.", "type": "string", "const": "store:allow-delete", "markdownDescription": "Enables the delete command without any pre-configured scope."}, {"description": "Enables the entries command without any pre-configured scope.", "type": "string", "const": "store:allow-entries", "markdownDescription": "Enables the entries command without any pre-configured scope."}, {"description": "Enables the get command without any pre-configured scope.", "type": "string", "const": "store:allow-get", "markdownDescription": "Enables the get command without any pre-configured scope."}, {"description": "Enables the get_store command without any pre-configured scope.", "type": "string", "const": "store:allow-get-store", "markdownDescription": "Enables the get_store command without any pre-configured scope."}, {"description": "Enables the has command without any pre-configured scope.", "type": "string", "const": "store:allow-has", "markdownDescription": "Enables the has command without any pre-configured scope."}, {"description": "Enables the keys command without any pre-configured scope.", "type": "string", "const": "store:allow-keys", "markdownDescription": "Enables the keys command without any pre-configured scope."}, {"description": "Enables the length command without any pre-configured scope.", "type": "string", "const": "store:allow-length", "markdownDescription": "Enables the length command without any pre-configured scope."}, {"description": "Enables the load command without any pre-configured scope.", "type": "string", "const": "store:allow-load", "markdownDescription": "Enables the load command without any pre-configured scope."}, {"description": "Enables the reload command without any pre-configured scope.", "type": "string", "const": "store:allow-reload", "markdownDescription": "Enables the reload command without any pre-configured scope."}, {"description": "Enables the reset command without any pre-configured scope.", "type": "string", "const": "store:allow-reset", "markdownDescription": "Enables the reset command without any pre-configured scope."}, {"description": "Enables the save command without any pre-configured scope.", "type": "string", "const": "store:allow-save", "markdownDescription": "Enables the save command without any pre-configured scope."}, {"description": "Enables the set command without any pre-configured scope.", "type": "string", "const": "store:allow-set", "markdownDescription": "Enables the set command without any pre-configured scope."}, {"description": "Enables the values command without any pre-configured scope.", "type": "string", "const": "store:allow-values", "markdownDescription": "Enables the values command without any pre-configured scope."}, {"description": "Denies the clear command without any pre-configured scope.", "type": "string", "const": "store:deny-clear", "markdownDescription": "Denies the clear command without any pre-configured scope."}, {"description": "Denies the delete command without any pre-configured scope.", "type": "string", "const": "store:deny-delete", "markdownDescription": "Denies the delete command without any pre-configured scope."}, {"description": "Denies the entries command without any pre-configured scope.", "type": "string", "const": "store:deny-entries", "markdownDescription": "Denies the entries command without any pre-configured scope."}, {"description": "Denies the get command without any pre-configured scope.", "type": "string", "const": "store:deny-get", "markdownDescription": "Denies the get command without any pre-configured scope."}, {"description": "Denies the get_store command without any pre-configured scope.", "type": "string", "const": "store:deny-get-store", "markdownDescription": "Denies the get_store command without any pre-configured scope."}, {"description": "Denies the has command without any pre-configured scope.", "type": "string", "const": "store:deny-has", "markdownDescription": "Denies the has command without any pre-configured scope."}, {"description": "Denies the keys command without any pre-configured scope.", "type": "string", "const": "store:deny-keys", "markdownDescription": "Denies the keys command without any pre-configured scope."}, {"description": "Denies the length command without any pre-configured scope.", "type": "string", "const": "store:deny-length", "markdownDescription": "Denies the length command without any pre-configured scope."}, {"description": "Denies the load command without any pre-configured scope.", "type": "string", "const": "store:deny-load", "markdownDescription": "Denies the load command without any pre-configured scope."}, {"description": "Denies the reload command without any pre-configured scope.", "type": "string", "const": "store:deny-reload", "markdownDescription": "Denies the reload command without any pre-configured scope."}, {"description": "Denies the reset command without any pre-configured scope.", "type": "string", "const": "store:deny-reset", "markdownDescription": "Denies the reset command without any pre-configured scope."}, {"description": "Denies the save command without any pre-configured scope.", "type": "string", "const": "store:deny-save", "markdownDescription": "Denies the save command without any pre-configured scope."}, {"description": "Denies the set command without any pre-configured scope.", "type": "string", "const": "store:deny-set", "markdownDescription": "Denies the set command without any pre-configured scope."}, {"description": "Denies the values command without any pre-configured scope.", "type": "string", "const": "store:deny-values", "markdownDescription": "Denies the values command without any pre-configured scope."}]}, "Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}, "Target": {"description": "Platform target.", "oneOf": [{"description": "MacOS.", "type": "string", "enum": ["macOS"]}, {"description": "Windows.", "type": "string", "enum": ["windows"]}, {"description": "Linux.", "type": "string", "enum": ["linux"]}, {"description": "Android.", "type": "string", "enum": ["android"]}, {"description": "iOS.", "type": "string", "enum": ["iOS"]}]}, "Application": {"description": "Opener scope application.", "anyOf": [{"description": "Open in default application.", "type": "null"}, {"description": "If true, allow open with any application.", "type": "boolean"}, {"description": "Allow specific application to open with.", "type": "string"}]}, "ShellScopeEntryAllowedArg": {"description": "A command argument allowed to be executed by the webview API.", "anyOf": [{"description": "A non-configurable argument that is passed to the command in the order it was specified.", "type": "string"}, {"description": "A variable that is set while calling the command from the webview API.", "type": "object", "required": ["validator"], "properties": {"raw": {"description": "Marks the validator as a raw regex, meaning the plugin should not make any modification at runtime.\n\nThis means the regex will not match on the entire string by default, which might be exploited if your regex allow unexpected input to be considered valid. When using this option, make sure your regex is correct.", "default": false, "type": "boolean"}, "validator": {"description": "[regex] validator to require passed values to conform to an expected input.\n\nThis will require the argument value passed to this variable to match the `validator` regex before it will be executed.\n\nThe regex string is by default surrounded by `^...$` to match the full string. For example the `https?://\\w+` regex would be registered as `^https?://\\w+$`.\n\n[regex]: <https://docs.rs/regex/latest/regex/#syntax>", "type": "string"}}, "additionalProperties": false}]}, "ShellScopeEntryAllowedArgs": {"description": "A set of command arguments allowed to be executed by the webview API.\n\nA value of `true` will allow any arguments to be passed to the command. `false` will disable all arguments. A list of [`ShellScopeEntryAllowedArg`] will set those arguments as the only valid arguments to be passed to the attached command configuration.", "anyOf": [{"description": "Use a simple boolean to allow all or disable all arguments to this command configuration.", "type": "boolean"}, {"description": "A specific set of [`ShellScopeEntryAllowedArg`] that are valid to call for the command configuration.", "type": "array", "items": {"$ref": "#/definitions/ShellScopeEntryAllowedArg"}}]}}}