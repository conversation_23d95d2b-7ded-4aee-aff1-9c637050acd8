# Y3技能编辑器UI设计方案（裁剪版：仅UI/校验/导出到Lua）

## 概述

本设计方案为网易英雄三国(Y3)实现技能编辑器UI，结合UE5蓝图系统的结构优势与Blender节点的视觉美感。核心采用事件驱动的逻辑图(Event-Driven Logic Graph)。
- 流程图：统一使用 React Flow（@xyflow/react）实现画布、节点与连线交互
- UI框架：统一使用 magicui 与 shadcn/ui 构建界面组件（按钮、菜单、表单、对话框等）
- 样式体系：统一使用 tailwindcss 进行样式设计与主题规范（全部组件遵循tailwind原子类约定）


## 整体架构设计

```mermaid
graph TD
    A[技能编辑器主界面] --> B[顶部工具栏]
    A --> C[可弹出/隐藏的左侧节点面板]
    A --> D[中央画布区域]
    A --> E[右侧属性面板（可变宽度）]
    A --> F[底部状态栏]
    
    B --> B1[文件操作]
    B --> B2[编辑工具]
    B --> B3[视图控制]
    B --> B4[面板切换按钮]
    B --> B6[导出到Lua]
    
    C --> C1[面板切换标签]
    C --> C2[节点搜索框]
    C --> C3[节点分类列表]
    
    D --> D1[React Flow画布]
    D --> D2[节点连接线]
    D --> D3[美化节点（Blender风格）]
    
    E --> E1[节点详细信息]
    E --> E2[基本/高级参数编辑]
    E --> E3[连接属性]
    E --> E4[上下文提示与自动修复]
    
    F --> F1[状态信息]
    F --> F2[错误/警告提示]
    F --> F3[导出校验状态]
```

## 节点类型设计

### 1. 事件节点（Event Nodes）

#### 1.1 Ability事件节点（覆盖Y3底层）
- `on_add` - 技能获得后执行
- `on_lose` - 技能失去后执行
- `on_switch` - 技能交换时执行
- `on_upgrade` - 技能升级后执行
- `on_cast_start` - 技能开始施法时执行
- `on_cast_channel` - 技能引导施法时执行（进入CD/消耗资源阶段）
- `on_cast_shot` - 技能出手施法时执行
- `on_cast_finish` - 技能完成施法时执行
- `on_cast_stop` - 技能停止施法时执行（施法事件的最后一个）
- `on_cast_interrupt` - 施法-打断开始
- `on_cast_channel_interrupt` - 施法-打断引导
- `on_cooldown` - 技能冷却结束后执行
- `on_active` - 技能激活时执行
- `on_deactive` - 技能失活时执行

事件节点统一输出上下文引脚：
- `self: Ability`、`owner: Unit`、`cast: Cast`

#### 1.2 Mover事件节点
- `on_hit` - 碰撞单位回调（可能重复触发，受 hit_same/hit_interval 影响）
- `on_block` - 碰撞地形回调（受 block_interval 影响）
- `on_finish` - 运动结束回调
- `on_break` - 运动打断回调
- `on_remove` - 运动移除回调

事件节点上下文输出：
- `ability?: Ability`、`unit?: Unit`、`mover: Mover`、`hit_unit?: Unit`

### 2. 动作节点（Action Nodes）

#### 2.1 Mover创建节点（带上下文自动注入）
- `创建直线运动器` (mover_line)
- `创建追踪运动器` (mover_target)
- `创建曲线运动器` (mover_curve)
- `创建环绕运动器` (mover_round)

上下文注入与约束：
- 若位于Ability事件流中，自动注入 `ability=self`、`unit=owner`
- 仅允许 Unit/Projectile/Particle 作为mover创建主体，类型不符禁止连接并提示原因

#### 2.2 Mover控制节点
- `打断运动器` (break_mover)
- `移除运动器` (remove_mover)

上下文要求：
- 需传入目标 `mover: Mover` 或引用最近创建的上下文Mover
- 若缺失，提供“从上游自动搜寻最近创建的Mover”的修复建议

### 3. 条件节点（Condition Nodes）
- 比较节点（等于、不等于、大于、小于等）
- 逻辑节点（AND、OR、NOT）
- 类型检查节点
- 条件节点具备 True/False 两个执行流输出引脚

### 4. 变量节点（Variable Nodes）
- 获取变量节点
- 设置变量节点
- 计算节点（数学运算、字符串操作等）
- 作用域支持：技能实例级、施法实例级、临时作用域

## 引脚设计（Blender风格）

```mermaid
graph TD
    subgraph 引脚设计
        A[执行引脚] --> A1[外圈: 灰黑色]
        A --> A2[内圈: 白色]
        
        B[数据引脚] --> B1[外圈: 灰黑色]
        B --> B2[内圈: 根据数据类型的颜色]
        
        C[输入引脚] --> C1[左侧位置]
        D[输出引脚] --> D1[右侧位置]
    end
```

### 引脚颜色编码（按类型）
- 执行流：白色内圈 + 灰黑色外圈
- 数值：蓝色内圈 + 灰黑色外圈
- 布尔：黄色内圈 + 灰黑色外圈
- 字符串：绿色内圈 + 灰黑色外圈
- 向量：橙色内圈 + 灰黑色外圈

### 引脚颜色编码（Y3核心对象专色）
- Ability：紫色内圈 + 灰黑色外圈
- Cast：青色内圈 + 灰黑色外圈
- Unit：深绿色内圈 + 灰黑色外圈
- Mover：玫红内圈 + 灰黑色外圈
- Projectile：蓝紫内圈 + 灰黑色外圈
- Particle：粉橙内圈 + 灰黑色外圈

## 节点设计（Blender风格美化）

### 节点视觉设计
- 圆角矩形（8px）、微渐变背景、轻阴影
- 类型主色：事件红(#e74c3c)、动作蓝(#3498db)、条件黄(#f1c40f)、变量绿(#2ecc71)
- 引脚：圆形，内圈彩色+外圈灰黑色
- 折叠/展开：折叠时保留关键参数快照行（如 angle/speed）

### 节点示例（创建直线运动器）
```
┌─────────────────────────────────────────────────┐
│ 🔷 创建直线运动器                    [▼]        │
├─────────────────────────────────────────────────┤
│ ○ exec ────────────────────────────────○ exec  │
├─────────────────────────────────────────────────┤
│ angle:    [360.0°]                    ● number  │
│ distance: [500.0]                     ● number  │
│ speed:    [300.0]                     ● number  │
│ 更多参数...                                      │
├─────────────────────────────────────────────────┤
│ unit:     [Unit]                      ○ Unit    │
│ ability:  [Ability]                   ○ Ability │
├─────────────────────────────────────────────────┤
│ mover:                                ● Mover   │
└─────────────────────────────────────────────────┘
```

## 左侧节点面板（可弹出/隐藏）

### 面板展开状态
```
┌─────────────────────────────────────────────────┐
│ 节点面板                     ×                  │
├─────────────────────────────────────────────────┤
│ 🔍 搜索节点...                                   │
├─────────────────────────────────────────────────┤
│ 📁 事件节点                                      │
│ │  🔴 on_add                                     │
│ │  🔴 on_switch                                  │
│ │  🔴 on_cast_start                              │
│ │  🔴 on_cast_channel                            │
│ │  🔴 on_cast_shot                               │
│ │  🔴 Mover事件                                  │
│ │    🔵 on_hit                                   │
│ │    🔵 on_block                                 │
│ │    🔵 on_finish                                │
├─────────────────────────────────────────────────┤
│ 📁 动作节点                                      │
│ │  🔷 创建直线运动器                             │
│ │  🔷 创建追踪运动器                             │
│ │  🔷 创建曲线运动器                             │
│ │  🔷 创建环绕运动器                             │
│ │  🔷 打断运动器                                 │
│ │  🔷 移除运动器                                 │
├─────────────────────────────────────────────────┤
│ 📁 条件节点                                      │
│ │  🟡 比较                                       │
│ │  🟡 逻辑运算                                   │
├─────────────────────────────────────────────────┤
│ 📁 变量节点                                      │
│ │  🟢 获取变量                                   │
│ │  🟢 设置变量                                   │
└─────────────────────────────────────────────────┘
```

### 面板折叠状态
```
┌────────┐
│ >      │
├────────┤
│ 🔍     │
├────────┤
│ 📁     │
├────────┤
│ 📁     │
├────────┤
│ 📁     │
├────────┤
│ 📁     │
└────────┘
```

## 右侧属性面板（可变宽度，Schema驱动）

### 面板结构
```mermaid
graph TD
    subgraph 右侧属性面板（可变宽度）
        A[面板标题栏] --> A1[节点名称]
        A --> A2[宽度调整手柄]
        
        B[基本参数] --> B1[参数组-基础]
        C[高级参数] --> C1[参数组-可选]
        D[连接信息] --> D1[输入连接]
        D --> D2[输出连接]
        E[上下文提示与修复] --> E1[缺失上下文的一键注入]
        F[节点注释] --> F1[注释输入框]
    end
```

### 宽度调整交互
- 拖动右边缘调整；双击自动适配
- 最小宽度200px；最大宽度800px；默认300px
- 宽度记忆写入工作区配置，不进入技能文件

### 基本/高级参数组示例
```
┌─────────────────────────────────────────────────┐
│ 🔷 创建直线运动器                    [|||]       │
├─────────────────────────────────────────────────┤
│ 基本参数                                          │
├─────────────────────────────────────────────────┤
│ 角度 angle (°)                                    │
│ ┌─────────────────────────────────────────────┐ │
│ │ [360.0]              [-] [+] step=1.0      │ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ 距离 distance                                   │
│ ┌─────────────────────────────────────────────┐ │
│ │ [500.0]              [-] [+] step=10       │ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ 速度 speed                                      │
│ ┌─────────────────────────────────────────────┐ │
│ │ [300.0]              [-] [+] step=10       │ │
│ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│ 高级参数                                          │
├─────────────────────────────────────────────────┤
│ 加速度 acceleration                              │
│ ┌─────────────────────────────────────────────┐ │
│ │ [0.0]                [-] [+]               │ │
│ └─────────────────────────────────────────────┘ │
│ 最大速度 max_speed                               │
│ 最小速度 min_speed                               │
└─────────────────────────────────────────────────┘
```

## 顶部工具栏设计（剔除调试/预览）

```mermaid
graph LR
    subgraph 顶部工具栏（UI基于 magicui/shadcn，样式统一使用 tailwind）
        A[文件] --> A1[新建技能]
        A --> A2[打开技能]
        A --> A3[保存技能]
        A --> A4[导出Lua]
        
        B[编辑] --> B1[撤销]
        B --> B2[重做]
        B --> B3[复制]
        B --> B4[粘贴]
        B --> B5[删除]
        
        C[视图] --> C1[缩放适应]
        C --> C2[重置视图]
        C --> C3[网格显示]
        C --> C4[对齐网格]
        
        D[面板] --> D1[切换节点面板]
        D --> D2[切换属性面板]
    end
```

## 底部状态栏设计

```mermaid
graph TD
    subgraph 底部状态栏
        A[状态信息] --> A1[节点数量]
        A --> A2[连接数量]
        A --> A3[导出校验状态]
        
        B[错误提示] --> B1[错误数量]
        B --> B2[警告数量]
        
        C[缩放控制] --> C1[缩放级别]
        C --> C2[网格选项]
    end
```

## 交互设计

### 1. 面板交互
- 左侧面板：展开/折叠、Tab快捷键切换、拖动调宽、折叠时悬停预览
- 右侧面板：拖动调宽、双击自适配、记忆宽度、参数组可折叠

### 2. 节点交互
- 节点移动/选择/多选/复制/删除
- 连接：从引脚拖拽创建；右键或快捷键断开；线条颜色按类型（连线组件基于 React Flow 扩展）
- 参数编辑：节点内联快速编辑；右侧面板完整编辑；数值拖拽微调；下拉选择（控件基于 magicui/shadcn + tailwind）

### 3. 右键上下文菜单（接管默认菜单）
- 画布空白处右键（菜单组件基于 magicui/shadcn）：
  - 新建节点：按分类/搜索创建，创建位置为光标处
  - 最近使用：快速插入最近使用的节点列表
  - 对齐/分布：对齐选中节点到网格、水平/垂直分布
  - 视图操作：缩放适应、重置视图、显示/隐藏网格（调用 React Flow API）
  - 撤销/重做
- 节点上右键：
  - 复制该节点、复制并粘贴到光标处
  - 删除节点（若有出入边，提示是否同时移除连线）
  - 折叠/展开节点
  - 设为模板/收藏（加入“最近使用/收藏”列表）
  - 选择同类型节点/同组节点
  - 导出该节点子图（仅结构到JSON片段）
- 连线上右键：
  - 断开连接
  - 在连线上插入节点（例如条件或变量节点）
  - 调整连线样式（曲线/直线）
- 交互细节：
  - 屏蔽浏览器默认contextmenu，统一使用自定义菜单（magicui/shadcn）
  - 菜单位置智能避让窗口边缘；支持键盘导航与Enter/Esc操作
  - 菜项依据上下文启用/禁用（无选中时禁用节点相关项）

## 技术实现要点

### 1. React Flow集成
- 流程图引擎：统一使用 React Flow（@xyflow/react）
- 能力范围：自定义节点与连线样式；拖拽/缩放/选择；连线校验钩子；右键菜单接管
- 一致性要求：所有画布级交互均基于 React Flow 的官方API扩展实现（不引入其他流程图库）

### 2. 节点组件与UI框架
- UI组件：统一使用 magicui 与 shadcn/ui 构建（按钮、菜单、抽屉、对话框、表单控件等）
- 样式：统一使用 tailwindcss，所有组件通过 tailwind 原子类或可复用样式约定实现
- Blender风格视觉；基本参数内联编辑；折叠/展开；
- 引脚组件视觉描述：中心为一枚圆形“核心”，使用低饱和、柔和且不过度发亮的颜色填充；外围包裹一圈细窄的灰黑色轮廓线（不厚重、对比度适中），整体呈现柔和而不刺眼的层次感

### 3. 属性面板组件（Schema驱动）
- UI实现：表单控件与分组采用 magicui/shadcn 组件组合；参数行样式统一由 tailwind 管理
- 以Schema驱动字段渲染：type、required、default、min/max、step、unit、dependsOn、visibleIf、doc
- 单一数据源：节点内联编辑与右侧编辑共享

### 4. 状态管理
- 画布UI状态：面板展开、宽度、缩放（UI组件采用 magicui/shadcn，样式使用 tailwind）
- 业务模型状态：Graph IR（节点/连线/变量/作用域/schemaVersion）
- 校验状态：错误/警告集合、定位与跳转

### 5. 数据验证与校验（静态）
- 编辑时：必填/范围/单位/依赖可见性即时提示（提示组件采用 shadcn/ui 的气泡/Toast）
- 连接时：类型严格匹配，Mover主体对象类型约束，缺失上下文提供自动注入建议（右键菜单用 magicui/shadcn 实现）
- 导出前：全量校验，错误分级（阻断/警告），定位到节点/引脚/参数并可一键跳转


## 数据IR与文件结构（新增）

- 类型系统：
  - 基础：number/boolean/string/vector
  - 对象：Ability/Cast/Unit/Mover/Projectile/Particle
  - 流：执行流/数据流；变量作用域：技能实例、施法实例、临时
- 节点/引脚/参数Schema字段：
  - `type`、`required`、`default`、`min/max`、`step`、`unit`、`dependsOn`、`visibleIf`、`doc`
- 技能文件（JSON）：
  - `meta`：id、name、version、schemaVersion、timestamps
  - `graph`：nodes、edges、variables、scopes
  - `ui`：节点坐标、面板宽度、折叠状态
- 版本化与迁移：按schemaVersion提供迁移器；加载旧文件时自动迁移并生成差异说明
- 结构化diff：对比nodes/edges/parameters差异，辅助审阅

## 导出到Lua（IR -> Lua）

- 产物：Lua脚本（必选）+ IR JSON（可选）
- 事件映射完整性：包含 on_switch 与 on_cast_channel
- 代码风格：一致命名、注释包含节点名称与关键参数
- 默认值兜底：导出阶段对缺省参数按Schema default填充
- 生成器接口：输入Graph IR，输出Lua字符串/文件；目录结构与文件命名规范化

## 实施计划（MVP为先）

1. 定义Graph IR与类型系统、Schema字段规范
2. React Flow基础画布 + Blender风格节点与引脚组件
3. 事件节点（on_add/on_switch/on_cast_start/on_cast_channel/on_cast_shot/on_cast_finish）
4. 动作节点（mover_line + break/remove）与上下文自动注入、对象类型约束
5. 条件节点（布尔 + 比较）与True/False执行分支
6. 变量节点（技能实例/施法实例作用域的获取/设置）
7. 右侧属性面板（Schema驱动渲染：基本/高级、单位/范围/步进、依赖显隐）
8. 连接与校验：类型阻止、上下文注入建议、导出前全量校验与定位
9. 导出IR->Lua最小通路、默认值兜底、注释化输出
10. 技能文件JSON结构、schemaVersion与基础迁移器；UI布局持久化（面板宽度写入工作区配置）
