## 类型定义

```lua
---@class (partial) Ability
---@field on_add? fun(self: self, owner: Unit) # 技能获得后执行
---@field on_lose? fun(self: self, owner: Unit) # 技能失去后执行
---@field on_switch? fun(self: self, owner: Unit) # 技能交换时执行.
---@field on_upgrade? fun(self: self, owner: Unit) # 技能升级后执行
---@field on_can_cast? fun(self: self, owner: Unit, cast: Cast) # 技能即将施法时执行
---@field on_cast_start? fun(self: self, owner: Unit, cast: Cast) # 技能开始施法时执行
---@field on_cast_channel? fun(self: self, owner: Unit, cast: Cast) # 技能引导施法时执行, 此阶段消耗资源 + 进入CD
---@field on_cast_shot? fun(self: self, owner: Unit, cast: Cast) # 技能出手施法时执行
---@field on_cast_finish? fun(self: self, owner: Unit, cast: Cast) # 技能完成施法时执行
---@field on_cast_stop? fun(self: self, owner: Unit, cast: Cast) # 技能停止施法时执, 所有施法事件的最后一个事件
---@field on_cast_interrupt? fun(self: self, owner: Unit, cast: Cast) # 施法-打断开始
---@field on_cast_channel_interrupt? fun(self: self, owner: Unit, cast: Cast) # 施法-打断引导
---@field on_cooldown? fun(self: self, owner: Unit) # 技能冷却结束后执行
---@field on_active? fun(self: self, owner: Unit) # 技能激活时执行, 在`on_add`之后. 该事件应该初始化允许在失活时移除的内容. 可重复执行.
---@field on_deactive? fun(self: self, owner: Unit) # 技能失活时执行, 在`on_lose`之前, `on_lose`必定会执行`on_deactive`. 该事件应该移除在激活时添加的内容. 可重复执行.

---@class Mover.CreateData.Base
---@field on_hit? fun(mover: Mover, unit: Unit) # 碰撞单位回调
---@field on_block? fun(mover: Mover) # 碰撞地形回调
---@field on_finish? fun(mover: Mover) # 运动结束回调
---@field on_break? fun(mover: Mover) # 运动打断回调
---@field on_remove? fun(mover: Mover) # 运动移除回调
---@field hit_type? integer # 碰撞类型 0： 敌人；1： 盟友；2： 全部
---@field hit_radius? number # 碰撞范围
---@field hit_same? boolean # 能否重复碰撞同一单位
---@field hit_interval? number # 碰撞同一个单位的间隔
---@field terrain_block? boolean # 是否会被地形阻挡
---@field block_interval? number # 触发地形阻挡事件的间隔
---@field priority? integer # 优先级
---@field absolute_height? boolean # 是否使用绝对高度
---@field face_angle? boolean # 是否始终面向运动方向
---@field ability? Ability # 关联技能
---@field unit? Unit # 关联单位
---@field auto_pitch? boolean # 是否自动调整俯仰角，默认为 `true`

---@class Mover.CreateData.Line: Mover.CreateData.Base
---@field angle number # 运动方向
---@field distance number # 运动距离
---@field speed number # 初始速度
---@field acceleration? number # 加速度
---@field max_speed? number # 最大速度
---@field min_speed? number # 最小速度
---@field init_height? number # 初始高度
---@field fin_height? number # 终点高度
---@field parabola_height? number # 抛物线顶点高度

---@class Mover.CreateData.Target: Mover.CreateData.Base
---@field target Unit|Destructible|Item # 追踪目标
---@field speed number # 初始速度
---@field target_distance number # 撞击目标的距离
---@field acceleration? number # 加速度
---@field max_speed? number # 最大速度
---@field min_speed? number # 最小速度
---@field height? number # 初始高度
---@field parabola_height? number # 抛物线顶点高度
---@field bind_point? string # 绑定点
---@field init_angle? number # 初始角度
---@field rotate_time? number # 过渡时间
---@field missing_distance? number #目标丢失距离
---@field miss_when_target_destroy? boolean #目标销毁时丢失目标
---@field on_miss? fun(self: Mover) #目标丢失时回调
---@field init_max_rotate_angle? number #初始角速度

---@class Mover.CreateData.Curve: Mover.CreateData.Base
---@field angle number # 运动方向
---@field distance number # 运动距离
---@field speed number # 初始速度
---@field path (Point|py.FixedVec2)[] # 路径点
---@field acceleration? number # 加速度
---@field max_speed? number # 最大速度
---@field min_speed? number # 最小速度
---@field init_height? number # 初始高度
---@field fin_height? number # 终点高度

---@class Mover.CreateData.Round: Mover.CreateData.Base
---@field target Unit|Point # 环绕目标
---@field radius? number # 环绕半径
---@field angle_speed? number # 环绕速度
---@field init_angle? number # 初始角度
---@field clock_wise? boolean # 是否顺时针
---@field round_time? number # 环绕时间
---@field radius_speed? number # 半径变化速度
---@field lifting_speed? number # 提升速度
---@field height? number # 环绕高度
---@field target_point? Point # 目标点
```

## 可用 mover 的对象

只有以下的对象才能够使用`mover`系统, 这是简略的代码实现.

```lua
---@class (partial) Unit
local Unit = Class 'Unit'

---@class (partial) Projectile
local Projectile = Class 'Projectile'

---@class (partial) Particle
local Particle = Class 'Particle'

---创建直线运动器
---@param mover_data Mover.CreateData.Line
---@return Mover
function Unit:mover_line(mover_data)
    local mover = M.mover_line(self, mover_data)
    return mover
end

---创建直线运动器
---@param mover_data Mover.CreateData.Line
---@return Mover
function Projectile:mover_line(mover_data)
    local mover = M.mover_line(self, mover_data)
    return mover
end

---创建直线运动器
---@param mover_data Mover.CreateData.Line
---@return Mover
function Particle:mover_line(mover_data)
    local mover = M.mover_line(self, mover_data)
    return mover
end

---创建追踪运动器
---@param mover_data Mover.CreateData.Target
---@return Mover
function Unit:mover_target(mover_data)
    local mover = M.mover_target(self, mover_data)
    return mover
end

---创建追踪运动器
---@param mover_data Mover.CreateData.Target
---@return Mover
function Projectile:mover_target(mover_data)
    local mover = M.mover_target(self, mover_data)
    return mover
end

---创建追踪运动器
---@param mover_data Mover.CreateData.Target
---@return Mover
function Particle:mover_target(mover_data)
    local mover = M.mover_target(self, mover_data)
    return mover
end

---创建曲线运动器
---@param mover_data Mover.CreateData.Curve
---@return Mover
function Unit:mover_curve(mover_data)
    local mover = M.mover_curve(self, mover_data)
    return mover
end

---创建曲线运动器
---@param mover_data Mover.CreateData.Curve
---@return Mover
function Projectile:mover_curve(mover_data)
    local mover = M.mover_curve(self, mover_data)
    return mover
end

---创建曲线运动器
---@param mover_data Mover.CreateData.Curve
---@return Mover
function Particle:mover_curve(mover_data)
    local mover = M.mover_curve(self, mover_data)
    return mover
end

---创建环绕运动器
---@param mover_data Mover.CreateData.Round
---@return Mover
function Unit:mover_round(mover_data)
    local mover = M.mover_round(self, mover_data)
    return mover
end

---创建环绕运动器
---@param mover_data Mover.CreateData.Round
---@return Mover
function Projectile:mover_round(mover_data)
    local mover = M.mover_round(self, mover_data)
    return mover
end

---创建环绕运动器
---@param mover_data Mover.CreateData.Round
---@return Mover
function Particle:mover_round(mover_data)
    local mover = M.mover_round(self, mover_data)
    return mover
end

---打断运动器
function Unit:break_mover()
    GameAPI.break_unit_mover(self.handle)
end

---移除运动器
function Unit:remove_mover()
    GameAPI.remove_unit_mover(self.handle)
end

---打断运动器
function Projectile:break_mover()
    -- TODO 见问题8
    ---@diagnostic disable-next-line: param-type-not-match
    GameAPI.break_unit_mover(self.handle)
end

---移除运动器
function Projectile:remove_mover()
    -- TODO 见问题8
    ---@diagnostic disable-next-line: param-type-not-match
    GameAPI.remove_unit_mover(self.handle)
end

return M
```