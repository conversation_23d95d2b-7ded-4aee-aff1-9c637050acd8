{"name": "y3skillflow", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "dev:debug": "cross-env WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS=--remote-debugging-port=9222 tauri dev"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "~2.3.2", "@tauri-apps/plugin-fs": "~2.4.1", "@tauri-apps/plugin-log": "~2.6.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "~2.3.0", "@tauri-apps/plugin-store": "~2.3.0", "@xyflow/react": "^12.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "motion": "^12.23.12", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "cross-env": "^10.0.0", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "vite": "^7.0.4"}}